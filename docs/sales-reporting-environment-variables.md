# Sales Reporting Environment Variables

This document outlines the environment variables required for the Stripe Sales Reporting feature.

## Required Environment Variables

### Slack Configuration
```bash
# Slack channel for sales reports (required)
SLACK_SALES_REPORT_CHANNEL=C1234567890

# Existing Slack token (already configured)
SLACK_TOKEN=xoxb-your-slack-bot-token
```

### Sales Reporting Feature Control
```bash
# Enable/disable sales reporting (default: false)
SALES_REPORTING_ENABLED=true

# Data retention period in days (default: 365)
SALES_DATA_RETENTION_DAYS=365
```

### Stripe Configuration (Already Configured)
```bash
# Production Stripe API key
STRIPE_API_KEY=sk_live_your_stripe_key

# Test Stripe API key (hardcoded in config for development)
# STRIPE_TEST_API_KEY is handled automatically based on customer name
```

## Environment-Specific Configuration

### Development Environment
```bash
SALES_REPORTING_ENABLED=true
SLACK_SALES_REPORT_CHANNEL=C_DEV_CHANNEL_ID
SALES_DATA_RETENTION_DAYS=30
```

### Production Environment
```bash
SALES_REPORTING_ENABLED=true
SLACK_SALES_REPORT_CHANNEL=C_PROD_CHANNEL_ID
SALES_DATA_RETENTION_DAYS=365
```

## Validation

The application will validate these environment variables at startup:

1. **SLACK_TOKEN** - Must be present for Slack integration
2. **SLACK_SALES_REPORT_CHANNEL** - Must be present if SALES_REPORTING_ENABLED=true
3. **STRIPE_API_KEY** - Must be present for production environment
4. **SALES_DATA_RETENTION_DAYS** - Must be a valid positive integer

## Setup Instructions

1. Add the required environment variables to your `.env` file
2. Restart the application to load new configuration
3. Verify configuration by calling the status endpoint: `GET /api/sales/v1/status`
4. Test the integration with: `POST /api/sales/v1/test-report`

## Troubleshooting

### Common Issues

**Sales reporting not working:**
- Check that `SALES_REPORTING_ENABLED=true`
- Verify `SLACK_SALES_REPORT_CHANNEL` is set to a valid channel ID
- Ensure the Slack bot has permission to post in the specified channel

**Database connection issues:**
- Verify existing database configuration (DB_HOST, DB_USER, etc.)
- Check that the stripe_payments table migration has been run

**Stripe integration issues:**
- Verify `STRIPE_API_KEY` is set correctly for your environment
- Check that webhook endpoints are properly configured in Stripe dashboard