# Quantity Increase Questionnaire API Endpoints Specification

## Overview
This document specifies the backend API endpoints needed to support the quantity increase questionnaire functionality. The frontend is implemented and expects these endpoints to be available.

## Database Table
The `quantity_increase_questionnaire` table should be created using the provided migration script (`database_migration_quantity_increase_questionnaire.sql`).

## Required API Endpoints

### 1. Submit Quantity Increase Questionnaire
**Endpoint:** `POST /funnel/v1.0/patient/quantity-increase-questionnaire`

**Purpose:** Submit a completed quantity increase questionnaire with scoring and strength selection

**Authentication:** Required (cookie-based, following existing patterns)

**Request Body:**
```json
{
  "questionsAndAnswers": [
    {
      "questionKey": "reasonForRequest",
      "questionText": "Why are you requesting an increase in the amount of medicinal cannabis approved under your current treatment plan?",
      "answerValue": ["The current quantity is not lasting the full month", "I need more frequent or higher doses to manage symptoms"],
      "answerText": "The current quantity is not lasting the full month, I need more frequent or higher doses to manage symptoms",
      "score": 5
    },
    {
      "questionKey": "currentEffectiveness",
      "questionText": "On a scale of 1 to 10, how well has your current quantity helped manage your symptoms?",
      "answerValue": "7",
      "answerText": "7",
      "score": 4
    },
    {
      "questionKey": "sideEffects",
      "questionText": "Have you experienced any side effects with your current dose?",
      "answerValue": ["Mild (e.g. dry mouth, light drowsiness)"],
      "answerText": "Mild (e.g. dry mouth, light drowsiness)",
      "score": 3
    },
    {
      "questionKey": "usageConsistency",
      "questionText": "Have you been using your current prescribed amount consistently?",
      "answerValue": "full-amount",
      "answerText": "Yes – I use my full monthly amount regularly",
      "score": 6
    },
    {
      "questionKey": "healthChanges",
      "questionText": "Have there been any changes in your health, medications, or lifestyle since your last doctor review?",
      "answerValue": "no-changes",
      "answerText": "No changes",
      "score": 3
    },
    {
      "questionKey": "expectations",
      "questionText": "What do you hope to achieve by increasing the quantity of your medicinal cannabis?",
      "answerValue": "Better symptom management throughout the month",
      "answerText": "Better symptom management throughout the month",
      "score": 2
    },
    {
      "questionKey": "concerns",
      "questionText": "Do you have any concerns about increasing the quantity?",
      "answerValue": "Worried about potential tolerance buildup",
      "answerText": "Worried about potential tolerance buildup",
      "score": 2
    },
    {
      "questionKey": "intendedUsage",
      "questionText": "How do you intend to use the increased quantity?",
      "answerValue": "worsening-symptoms",
      "answerText": "To manage worsening or more frequent symptoms",
      "score": 4
    },
    {
      "questionKey": "consent",
      "questionText": "Do you consent to your doctor reviewing this information and accessing your My Health Record (if needed) to determine whether a quantity increase is clinically appropriate?",
      "answerValue": "yes",
      "answerText": "Yes",
      "score": 5
    }
  ],
  "selectedStrengths": ["29"],
  "strengthRequests": [
    {
      "strength": "29",
      "currentQuantity": 28,
      "requestedQuantity": 42,
      "increaseAmount": 14
    }
  ],
  "totalScore": 34,
  "maxScore": 50,
  "isEligible": false,
  "submittedAt": "2025-01-17T10:30:00Z"
}
```

**Example with Multiple Strengths:**
```json
{
  "selectedStrengths": ["22", "29"],
  "strengthRequests": [
    {
      "strength": "22",
      "currentQuantity": 14,
      "requestedQuantity": 28,
      "increaseAmount": 14
    },
    {
      "strength": "29",
      "currentQuantity": 42,
      "requestedQuantity": 70,
      "increaseAmount": 28
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Quantity increase questionnaire submitted successfully",
  "questionnaireId": "uuid-here",
  "isEligible": false,
  "totalScore": 34,
  "maxScore": 50,
  "strengthRequests": [
    {
      "strength": "22",
      "currentQuantity": 14,
      "requestedQuantity": 28,
      "increaseAmount": 14
    },
    {
      "strength": "29",
      "currentQuantity": 42,
      "requestedQuantity": 70,
      "increaseAmount": 28
    }
  ]
}
```

**Implementation Notes:**
- Extract user email from authentication session/cookie
- Look up patient_id from the `patient` table using email
- Validate that the selected strength and quantities are valid for this patient
- Store the form data in the `questionnaire_data` JSONB column
- Set `total_score`, `is_eligible`, and `status` fields
- Include IP address and user agent in metadata
- Return success response with questionnaire ID and eligibility status

### 2. Get Quantity Increase Questionnaire Status
**Endpoint:** `GET /funnel/v1.0/patient/quantity-increase-questionnaire/status?email={email}`

**Purpose:** Check if user has completed the quantity increase questionnaire and get their status

**Authentication:** Required (cookie-based)

**Query Parameters:**
- `email` (required): Patient email address

**Response:**
```json
{
  "success": true,
  "questionnaire": {
    "totalScore": 34,
    "isEligible": false,
    "status": "submitted",
    "selectedStrength": "29",
    "currentQuantity": 28,
    "requestedQuantity": 42,
    "submittedAt": "2025-01-17T10:30:00Z"
  }
}
```

**Response when no questionnaire found:**
```json
{
  "success": true,
  "questionnaire": null
}
```

**Implementation Notes:**
- Look up questionnaire by patient email
- Return the most recent questionnaire if multiple exist
- Include strength and quantity information for display purposes
- Status can be: 'submitted', 'under_review', 'approved', 'rejected'

### 3. Get Patient Quantity Status (Helper Endpoint)
**Endpoint:** `GET /funnel/v1.0/patient/quantity-status?email={email}`

**Purpose:** Get patient's current quantities and available increase options

**Authentication:** Required (cookie-based)

**Query Parameters:**
- `email` (required): Patient email address

**Response:**
```json
{
  "success": true,
  "quantityStatus": {
    "thc22": {
      "current": 14,
      "canIncrease": true,
      "nextLevel": 28,
      "maxLevel": 84
    },
    "thc29": {
      "current": 28,
      "canIncrease": true,
      "nextLevel": 42,
      "maxLevel": 84
    },
    "hasAnyIncreaseOptions": true
  },
  "availableOptions": [
    {
      "value": "22",
      "label": "22% THC (14g → 28g)",
      "current": 14,
      "next": 28
    },
    {
      "value": "29", 
      "label": "29% THC (28g → 42g)",
      "current": 28,
      "next": 42
    }
  ]
}
```

**Implementation Notes:**
- Fetch patient's current treatment plan
- Calculate available increase options using the quantity progression logic
- Return structured data for frontend to display options
- This endpoint can be used to validate eligibility before showing the questionnaire

## Database Schema Requirements

The questionnaire data should be stored in a dedicated table with the following structure:

- `id` (UUID, Primary Key)
- `patient_id` (UUID, Foreign Key to patient table)
- `email` (VARCHAR, for quick lookups)
- `zoho_id` (VARCHAR, if available)
- `questionnaire_data` (JSONB, stores the complete questionnaire response)
- `selected_strength` (VARCHAR, '22' or '29')
- `current_quantity` (INTEGER, patient's current quantity)
- `requested_quantity` (INTEGER, requested new quantity)
- `total_score` (INTEGER)
- `max_score` (INTEGER, typically 50)
- `is_eligible` (BOOLEAN)
- `status` (VARCHAR, 'submitted'|'under_review'|'approved'|'rejected')
- `reviewed_by` (VARCHAR, doctor who reviewed)
- `reviewed_at` (TIMESTAMP)
- `review_notes` (TEXT)
- `created_at` (TIMESTAMP, auto-generated)
- `updated_at` (TIMESTAMP, auto-updated)

## Error Handling

All endpoints should return appropriate HTTP status codes:
- 200: Success
- 400: Bad request (validation errors)
- 401: Unauthorized
- 404: Resource not found
- 500: Internal server error

Error response format:
```json
{
  "success": false,
  "error": "Error message here",
  "details": "Additional error details if applicable"
}
```

## Security Considerations

- All endpoints require authentication
- Validate that users can only access their own questionnaire data
- Sanitize all input data before storage
- Log all questionnaire submissions for audit purposes
- Rate limiting should be applied to prevent abuse
