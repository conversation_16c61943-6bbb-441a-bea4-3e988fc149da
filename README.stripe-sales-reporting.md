# Stripe Sales Reporting System

A comprehensive sales reporting system that automatically generates daily, weekly, and monthly sales reports from Stripe payment data and delivers them to Slack.

## Overview

This system integrates with your existing Stripe webhook processing to collect payment data and generates automated reports that are sent to Slack channels. The reports include payment statistics, success rates, customer information (privacy-compliant), and trend analysis.

## Features

- **Automated Reports**: Daily, weekly, and monthly sales reports
- **Slack Integration**: Professional formatted reports delivered to Slack
- **Trend Analysis**: Comparison with previous periods and growth metrics
- **Privacy Compliant**: Shows customer names but protects sensitive payment amounts
- **Non-Breaking Integration**: Enhances existing webhook processing without disruption
- **Comprehensive Testing**: Full test coverage with unit, integration, and E2E tests
- **Monitoring & Logging**: Detailed logging and error handling throughout

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Cron Jobs     │───▶│   API Endpoints  │───▶│ Sales Service   │
│ (Linux Server)  │    │  /sales/trigger  │    │   (Business     │
└─────────────────┘    └──────────────────┘    │    Logic)       │
                                               └─────────────────┘
                                                        │
┌─────────────────┐    ┌──────────────────┐           │
│ Stripe Webhooks │───▶│ Enhanced Webhook │           │
│   (External)    │    │    Handler       │           │
└─────────────────┘    └──────────────────┘           │
                                │                      │
                                ▼                      ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   Database       │    │ Slack API       │
                       │ (stripe_payments)│    │ (Formatted      │
                       └──────────────────┘    │  Reports)       │
                                               └─────────────────┘
```

## Installation

### 1. Database Setup

Run the database migration to create the required table:

```sql
-- Run this migration file
migrations/create_stripe_payments_table.sql
```

### 2. Environment Variables

Configure the following environment variables:

```bash
# Required for sales reporting
SALES_REPORTING_ENABLED=true
SLACK_SALES_REPORT_CHANNEL=C1234567890

# Existing variables (should already be configured)
SLACK_TOKEN=xoxb-your-slack-bot-token
STRIPE_API_KEY=sk_live_your_stripe_key

# Optional configuration
SALES_DATA_RETENTION_DAYS=365
```

See [Environment Variables Documentation](docs/sales-reporting-environment-variables.md) for complete details.

### 3. Cron Job Setup

Set up automated cron jobs on your Linux server:

```bash
# Edit crontab
crontab -e

# Add these lines:
# Daily report at 9:00 AM
0 9 * * * curl -X POST https://your-domain.com/api/sales/v1/trigger-daily-report

# Weekly report every Monday at 9:00 AM  
0 9 * * 1 curl -X POST https://your-domain.com/api/sales/v1/trigger-weekly-report

# Monthly report on 1st of each month at 9:00 AM
0 9 1 * * curl -X POST https://your-domain.com/api/sales/v1/trigger-monthly-report
```

See [Cron Job Setup Documentation](docs/sales-reporting-cron-setup.md) for complete details.

## API Endpoints

### Report Triggers (for Cron Jobs)

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/sales/v1/trigger-daily-report` | POST | Generate and send daily sales report |
| `/api/sales/v1/trigger-weekly-report` | POST | Generate and send weekly sales report |
| `/api/sales/v1/trigger-monthly-report` | POST | Generate and send monthly sales report |

### Monitoring & Testing

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/sales/v1/status` | GET | Get system status and configuration |
| `/api/sales/v1/test-report` | POST | Send test report to Slack |
| `/api/sales/v1/data` | GET | Get sales data for date range |

### Example API Calls

```bash
# Check system status
curl https://your-domain.com/api/sales/v1/status

# Send test report
curl -X POST https://your-domain.com/api/sales/v1/test-report

# Get sales data for date range
curl "https://your-domain.com/api/sales/v1/data?startDate=2024-01-01&endDate=2024-01-31"

# Trigger daily report manually
curl -X POST https://your-domain.com/api/sales/v1/trigger-daily-report
```

## Report Examples

### Daily Report
```
💰 Daily Sales Report
Generated: 9:00 AM, 16 Jan 2024 | Report Date: 15 Jan 2024

Daily Summary for 15 Jan 2024
• Total Payments: 12
• Successful: 10
• Failed: 2
• Success Rate: 83.3%
• Total Revenue: $299.90

Customer Payments:
✅ John Doe (<EMAIL>) - $29.99 at 10:30
✅ Jane Smith (<EMAIL>) - $29.99 at 14:15
❌ Bob Wilson (<EMAIL>) - $29.99 at 16:45
```

### Weekly Report
```
📊 Weekly Sales Report
Generated: 9:00 AM, 22 Jan 2024 | Week 3: 15 Jan - 21 Jan 2024

Weekly Summary
• Total Payments: 67 📈 (+12.5%)
• Successful: 58
• Failed: 9
• Success Rate: 86.6%
• Total Revenue: $1,736.42

Daily Breakdown:
Day       Date     Payments  Success Rate
─────────────────────────────────────────
Mon       Jan 15   12        83%
Tue       Jan 16   8         100%
Wed       Jan 17   15        87%
Thu       Jan 18   11        91%
Fri       Jan 19   13        85%
Sat       Jan 20   5         80%
Sun       Jan 21   3         100%
```

## Configuration

### Feature Flags

```typescript
// Enable/disable sales reporting
SALES_REPORTING_ENABLED=true

// Data retention period
SALES_DATA_RETENTION_DAYS=365
```

### Slack Configuration

```bash
# Slack channel for reports
SLACK_SALES_REPORT_CHANNEL=C1234567890

# Slack bot token (existing)
SLACK_TOKEN=xoxb-your-slack-bot-token
```

## Development

### Running Tests

```bash
# Run all tests
npm test

# Run sales reporting tests specifically
npm test -- --testPathPattern=sales

# Run with coverage
npm test -- --coverage
```

### Local Development

```bash
# Start the API server
npm run dev

# Test endpoints locally
curl http://localhost:5000/api/sales/v1/status
curl -X POST http://localhost:5000/api/sales/v1/test-report
```

### Adding New Report Types

1. Add new method to `SalesReportingService`
2. Create corresponding API endpoint in `sales/index.ts`
3. Add route configuration in `sales.route.ts`
4. Add tests for new functionality
5. Update documentation

## Monitoring

### Health Checks

```bash
# Check overall system health
curl https://your-domain.com/health

# Check sales reporting status
curl https://your-domain.com/api/sales/v1/status
```

### Log Monitoring

```bash
# View application logs
tail -f /var/log/your-app.log

# View cron job logs
tail -f /var/log/sales-reports.log

# View system cron logs
sudo tail -f /var/log/cron
```

### Slack Monitoring

- Reports are sent to the configured Slack channel
- Failed reports will appear in application logs
- Test reports can be sent manually for verification

## Troubleshooting

### Common Issues

#### 1. Reports Not Being Generated

**Check cron jobs:**
```bash
crontab -l
sudo systemctl status cron
```

**Check API endpoints:**
```bash
curl https://your-domain.com/api/sales/v1/status
```

**Check logs:**
```bash
tail -f /var/log/sales-reports.log
```

#### 2. Slack Integration Issues

**Verify configuration:**
```bash
# Check if channel is configured
echo $SLACK_SALES_REPORT_CHANNEL

# Test Slack connectivity
curl -X POST https://your-domain.com/api/sales/v1/test-report
```

**Check permissions:**
- Verify Slack bot has permission to post in the channel
- Ensure channel ID is correct (not channel name)

#### 3. Database Issues

**Check table exists:**
```sql
SELECT * FROM stripe_payments LIMIT 1;
```

**Check recent data:**
```sql
SELECT COUNT(*) FROM stripe_payments WHERE created_at >= CURRENT_DATE;
```

#### 4. Webhook Integration Issues

**Check webhook processing:**
```bash
# Look for sales reporting logs in webhook processing
grep "Payment data stored for sales reporting" /var/log/your-app.log
```

**Verify feature flag:**
```bash
echo $SALES_REPORTING_ENABLED
```

### Debug Mode

Enable debug logging by setting:
```bash
LOG_LEVEL=debug
```

## Security

### Data Privacy
- Customer names are included in reports but payment amounts are aggregated
- Sensitive payment details are not logged in plain text
- Database access is restricted to application service account

### API Security
- Cron job endpoints are designed for server-level access
- Consider adding authentication for production environments
- Use HTTPS for all API communications

### Environment Security
- Store all API keys and tokens as environment variables
- Use different Slack channels for different environments
- Implement proper log rotation and retention policies

## Performance

### Database Optimization
- Indexes are created on frequently queried columns
- Queries use date ranges to limit data processing
- Connection pooling is used for concurrent operations

### Report Generation
- Reports are generated asynchronously
- Large date ranges are processed in chunks
- Caching can be added for frequently accessed data

### Slack API
- Rate limiting is handled with retry logic
- Block formatting is used for efficient message delivery
- Error handling prevents webhook processing failures

## Support

### Documentation
- [Environment Variables](docs/sales-reporting-environment-variables.md)
- [Cron Job Setup](docs/sales-reporting-cron-setup.md)
- [API Documentation](docs/api-endpoints.md)

### Monitoring
- System status: `GET /api/sales/v1/status`
- Test functionality: `POST /api/sales/v1/test-report`
- View logs: `/var/log/sales-reports.log`

### Contact
For issues or questions:
1. Check the troubleshooting section above
2. Review application logs for detailed error messages
3. Test individual components using the provided endpoints
4. Verify configuration using the status endpoint