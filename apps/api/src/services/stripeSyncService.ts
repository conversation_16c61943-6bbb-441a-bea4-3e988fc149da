import { DateTime } from 'luxon';
import { PoolClient } from 'pg';
import { db } from '../utils/db';
import { logger } from '../config/logger';
import StripeAPIService, { StripePaymentData } from './stripeAPI.service';

interface SyncResult {
  success: boolean;
  paymentsProcessed: number;
  paymentsInserted: number;
  paymentsUpdated: number;
  errors: string[];
  syncDuration: number;
}

interface SyncOptions {
  startDate: DateTime;
  endDate: DateTime;
  syncType: 'daily' | 'weekly' | 'manual' | 'backfill';
  batchSize?: number;
}

interface SyncLogData {
  id: string;
  sync_date: string;
  sync_type: string;
  payments_synced: number;
  sync_status: string;
  error_message: string | null;
  started_at: Date;
  completed_at: Date | null;
}

class StripeSyncService {
  private stripeAPI: StripeAPIService;
  private readonly maxRetries = 3;
  private readonly retryDelays = [1000, 2000, 5000]; // Progressive delays in ms
  private readonly maxSyncDuration = 30 * 60 * 1000; // 30 minutes max

  constructor() {
    this.stripeAPI = new StripeAPIService();
  }

  /**
   * Sync payments for a specific date range
   */
  async syncPaymentsForDateRange(options: SyncOptions): Promise<SyncResult> {
    const startTime = Date.now();
    const { startDate, endDate, syncType, batchSize = 100 } = options;
    
    logger.info(`Starting ${syncType} sync for ${startDate.toISO()} to ${endDate.toISO()}`);

    const result: SyncResult = {
      success: false,
      paymentsProcessed: 0,
      paymentsInserted: 0,
      paymentsUpdated: 0,
      errors: [],
      syncDuration: 0,
    };

    const client = await db.connect();

    try {
      await client.query('BEGIN');

      // Log sync start
      const syncLogId = await this.logSyncStart(client, {
        syncDate: startDate.toFormat('yyyy-MM-dd'),
        syncType,
        startedAt: new Date(),
      });

      // Fetch payments from Stripe API
      const payments = await this.stripeAPI.fetchAllPaymentsForDateRange({
        startDate,
        endDate,
        limit: batchSize,
      });

      logger.info(`Fetched ${payments.length} payments from Stripe API`);

      // Process payments in batches
      for (let i = 0; i < payments.length; i += batchSize) {
        const batch = payments.slice(i, i + batchSize);
        const batchResult = await this.processBatch(client, batch);
        
        result.paymentsProcessed += batchResult.processed;
        result.paymentsInserted += batchResult.inserted;
        result.paymentsUpdated += batchResult.updated;
        result.errors.push(...batchResult.errors);

        logger.info(`Processed batch ${Math.floor(i / batchSize) + 1}: ${batchResult.processed} payments`);
      }

      // Update sync log
      await this.logSyncComplete(client, syncLogId, {
        paymentsProcessed: result.paymentsProcessed,
        status: result.errors.length === 0 ? 'success' : 'partial',
        errorMessage: result.errors.length > 0 ? result.errors.join('; ') : null,
        completedAt: new Date(),
      });

      await client.query('COMMIT');
      
      result.success = true;
      result.syncDuration = Date.now() - startTime;

      logger.info(`Sync completed successfully: ${result.paymentsProcessed} processed, ${result.paymentsInserted} inserted, ${result.paymentsUpdated} updated`);

    } catch (error) {
      await client.query('ROLLBACK');
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      result.errors.push(errorMessage);
      result.syncDuration = Date.now() - startTime;
      
      logger.error('Sync failed:', error);
      throw error;
    } finally {
      client.release();
    }

    return result;
  }

  /**
   * Sync July 23rd payments (daily sync)
   */
  async syncDailyPayments(): Promise<SyncResult> {
    // Fixed date: July 23, 2025
    const targetDate = DateTime.fromObject(
      { year: 2025, month: 7, day: 23 },
      { zone: 'Australia/Sydney' }
    );
    const startOfDay = targetDate.startOf('day');
    const endOfDay = targetDate.endOf('day');

    return this.syncPaymentsForDateRange({
      startDate: startOfDay,
      endDate: endOfDay,
      syncType: 'daily',
    });
  }

  /**
   * Sync payments for the last N days (weekly catchup)
   */
  async syncWeeklyPayments(daysBack: number = 7): Promise<SyncResult> {
    const now = DateTime.now().setZone('Australia/Sydney');
    const startDate = now.minus({ days: daysBack }).startOf('day');
    const endDate = now.minus({ days: 1 }).endOf('day');

    return this.syncPaymentsForDateRange({
      startDate,
      endDate,
      syncType: 'weekly',
    });
  }

  /**
   * Backfill historical payments
   */
  async backfillPayments(daysBack: number): Promise<SyncResult> {
    const now = DateTime.now().setZone('Australia/Sydney');
    const startDate = now.minus({ days: daysBack }).startOf('day');
    const endDate = now.minus({ days: 1 }).endOf('day');

    return this.syncPaymentsForDateRange({
      startDate,
      endDate,
      syncType: 'backfill',
      batchSize: 50, // Smaller batches for historical data
    });
  }

  /**
   * Process a batch of payments with validation and enhanced error handling
   */
  private async processBatch(client: PoolClient, payments: StripePaymentData[]): Promise<{
    processed: number;
    inserted: number;
    updated: number;
    errors: string[];
  }> {
    const result: {
      processed: number;
      inserted: number;
      updated: number;
      errors: string[];
    } = {
      processed: 0,
      inserted: 0,
      updated: 0,
      errors: [],
    };

    // Validate all payments first
    const validPayments: StripePaymentData[] = [];

    for (const payment of payments) {
      const validation = this.validatePaymentData(payment);
      if (validation.valid) {
        validPayments.push(payment);
      } else {
        const errorMessage = `Invalid payment data for ${payment.stripePaymentIntentId}: ${validation.errors.join(', ')}`;
        result.errors.push(errorMessage);
        logger.warn(errorMessage);
      }
    }

    // Process valid payments
    if (validPayments.length > 0) {
      try {
        // Use bulk upsert for better performance
        const bulkResult = await this.bulkUpsertPayments(client, validPayments);

        result.processed += bulkResult.processed;
        result.inserted += bulkResult.inserted;
        result.updated += bulkResult.updated;
        result.errors.push(...bulkResult.errors);

      } catch (error) {
        // Fallback to individual processing if bulk fails
        logger.warn('Bulk upsert failed, falling back to individual processing:', error);

        for (const payment of validPayments) {
          try {
            const upsertResult = await this.upsertPaymentData(client, payment);
            result.processed++;

            if (upsertResult.inserted) {
              result.inserted++;
            } else {
              result.updated++;
            }
          } catch (individualError) {
            const errorMessage = `Failed to process payment ${payment.stripePaymentIntentId}: ${individualError instanceof Error ? individualError.message : 'Unknown error'}`;
            result.errors.push(errorMessage);
            logger.error(errorMessage);
          }
        }
      }
    }

    return result;
  }

  /**
   * Upsert payment data into database with enhanced conflict resolution
   */
  private async upsertPaymentData(client: PoolClient, paymentData: StripePaymentData): Promise<{
    inserted: boolean;
    updated: boolean;
    previousStatus?: string;
    statusChanged: boolean;
  }> {
    try {
      // First, check if payment exists and get current status
      const existingQuery = `
        SELECT status, synced_at
        FROM stripe_payments
        WHERE stripe_payment_intent_id = $1
      `;

      const existingResult = await client.query(existingQuery, [paymentData.stripePaymentIntentId]);
      const existingPayment = existingResult.rows[0];

      // Perform upsert with detailed tracking
      const upsertQuery = `
        INSERT INTO stripe_payments
        (stripe_payment_intent_id, customer_name, customer_email, amount_cents, currency, status, description, metadata, created_at, synced_at, sync_source)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP, 'api')
        ON CONFLICT (stripe_payment_intent_id)
        DO UPDATE SET
          customer_name = CASE
            WHEN EXCLUDED.customer_name != 'Unknown' THEN EXCLUDED.customer_name
            ELSE stripe_payments.customer_name
          END,
          customer_email = CASE
            WHEN EXCLUDED.customer_email != '<EMAIL>' THEN EXCLUDED.customer_email
            ELSE stripe_payments.customer_email
          END,
          status = EXCLUDED.status,
          description = CASE
            WHEN EXCLUDED.description != '' THEN EXCLUDED.description
            ELSE stripe_payments.description
          END,
          metadata = EXCLUDED.metadata,
          synced_at = CURRENT_TIMESTAMP,
          sync_source = 'api'
        RETURNING (xmax = 0) AS inserted
      `;

      const upsertResult = await client.query(upsertQuery, [
        paymentData.stripePaymentIntentId,
        paymentData.customerName,
        paymentData.customerEmail,
        paymentData.amountCents,
        paymentData.currency,
        paymentData.status,
        paymentData.description,
        JSON.stringify(paymentData.metadata),
        paymentData.createdAt,
      ]);

      const inserted = upsertResult.rows[0].inserted;
      const updated = !inserted;
      const statusChanged = existingPayment && existingPayment.status !== paymentData.status;

      // Log significant changes
      if (statusChanged) {
        logger.info(`Payment status changed: ${paymentData.stripePaymentIntentId} from ${existingPayment.status} to ${paymentData.status}`);
      }

      if (updated) {
        logger.info(`Payment updated: ${paymentData.stripePaymentIntentId}`);
      }

      return {
        inserted,
        updated,
        previousStatus: existingPayment?.status,
        statusChanged: !!statusChanged,
      };

    } catch (error) {
      logger.error(`Failed to upsert payment ${paymentData.stripePaymentIntentId}:`, error);
      throw error;
    }
  }

  /**
   * Bulk upsert payments for better performance
   */
  private async bulkUpsertPayments(client: PoolClient, payments: StripePaymentData[]): Promise<{
    processed: number;
    inserted: number;
    updated: number;
    errors: string[];
  }> {
    const result: {
      processed: number;
      inserted: number;
      updated: number;
      errors: string[];
    } = {
      processed: 0,
      inserted: 0,
      updated: 0,
      errors: [],
    };

    // Process in smaller chunks to avoid memory issues
    const chunkSize = 10;

    for (let i = 0; i < payments.length; i += chunkSize) {
      const chunk = payments.slice(i, i + chunkSize);

      try {
        // Use a savepoint for each chunk
        await client.query('SAVEPOINT chunk_upsert');

        for (const payment of chunk) {
          try {
            const upsertResult = await this.upsertPaymentData(client, payment);
            result.processed++;

            if (upsertResult.inserted) {
              result.inserted++;
            } else {
              result.updated++;
            }
          } catch (error) {
            const errorMessage = `Failed to process payment ${payment.stripePaymentIntentId}: ${error instanceof Error ? error.message : 'Unknown error'}`;
            result.errors.push(errorMessage);
            logger.error(errorMessage);
          }
        }

        await client.query('RELEASE SAVEPOINT chunk_upsert');

      } catch (error) {
        await client.query('ROLLBACK TO SAVEPOINT chunk_upsert');
        const errorMessage = `Failed to process chunk starting at index ${i}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        result.errors.push(errorMessage);
        logger.error(errorMessage);
      }
    }

    return result;
  }

  /**
   * Validate payment data before upsert
   */
  private validatePaymentData(paymentData: StripePaymentData): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Required fields validation
    if (!paymentData.stripePaymentIntentId) {
      errors.push('Missing stripe_payment_intent_id');
    }

    if (!paymentData.stripePaymentIntentId.startsWith('pi_')) {
      errors.push('Invalid stripe_payment_intent_id format');
    }

    if (typeof paymentData.amountCents !== 'number' || paymentData.amountCents < 0) {
      errors.push('Invalid amount_cents value');
    }

    if (!paymentData.currency || paymentData.currency.length !== 3) {
      errors.push('Invalid currency code');
    }

    if (!paymentData.status) {
      errors.push('Missing payment status');
    }

    // Email validation
    if (paymentData.customerEmail && !this.isValidEmail(paymentData.customerEmail)) {
      errors.push('Invalid customer email format');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Email validation helper
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Log sync start
   */
  private async logSyncStart(client: PoolClient, data: {
    syncDate: string;
    syncType: string;
    startedAt: Date;
  }): Promise<string> {
    const query = `
      INSERT INTO stripe_sync_log (sync_date, sync_type, sync_status, started_at)
      VALUES ($1, $2, 'running', $3)
      RETURNING id
    `;

    const result = await client.query(query, [data.syncDate, data.syncType, data.startedAt]);
    return result.rows[0].id;
  }

  /**
   * Log sync completion
   */
  private async logSyncComplete(client: PoolClient, syncLogId: string, data: {
    paymentsProcessed: number;
    status: string;
    errorMessage: string | null;
    completedAt: Date;
  }): Promise<void> {
    const query = `
      UPDATE stripe_sync_log 
      SET payments_synced = $1, sync_status = $2, error_message = $3, completed_at = $4
      WHERE id = $5
    `;

    await client.query(query, [
      data.paymentsProcessed,
      data.status,
      data.errorMessage,
      data.completedAt,
      syncLogId,
    ]);
  }

  /**
   * Get sync status for a specific date
   */
  async getSyncStatus(date: string): Promise<SyncLogData | null> {
    const client = await db.connect();
    
    try {
      const query = `
        SELECT * FROM stripe_sync_log 
        WHERE sync_date = $1 
        ORDER BY started_at DESC 
        LIMIT 1
      `;
      
      const result = await client.query(query, [date]);
      return result.rows[0] || null;
    } finally {
      client.release();
    }
  }

  /**
   * Get recent sync history
   */
  async getRecentSyncHistory(limit: number = 10): Promise<SyncLogData[]> {
    const client = await db.connect();

    try {
      const query = `
        SELECT * FROM stripe_sync_log
        ORDER BY started_at DESC
        LIMIT $1
      `;

      const result = await client.query(query, [limit]);
      return result.rows;
    } finally {
      client.release();
    }
  }

  /**
   * Execute operation with retry logic
   */
  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    operationName: string,
    maxRetries: number = this.maxRetries
  ): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        logger.info(`Executing ${operationName} (attempt ${attempt + 1}/${maxRetries + 1})`);
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');

        if (attempt === maxRetries) {
          logger.error(`${operationName} failed after ${maxRetries + 1} attempts:`, lastError);
          break;
        }

        const delay = this.retryDelays[attempt] || this.retryDelays[this.retryDelays.length - 1];
        const shouldRetry = this.shouldRetryError(lastError);

        if (!shouldRetry) {
          logger.error(`${operationName} failed with non-retryable error:`, lastError);
          break;
        }

        logger.warn(`${operationName} failed (attempt ${attempt + 1}), retrying in ${delay}ms:`, lastError.message);
        await this.delay(delay);
      }
    }

    throw lastError || new Error(`${operationName} failed after retries`);
  }

  /**
   * Determine if an error should trigger a retry
   */
  private shouldRetryError(error: Error): boolean {
    const retryableErrors = [
      'ECONNRESET',
      'ENOTFOUND',
      'ECONNREFUSED',
      'ETIMEDOUT',
      'rate_limit',
      'api_connection_error',
      'api_error',
    ];

    const errorMessage = error.message.toLowerCase();
    const errorName = error.name.toLowerCase();

    return retryableErrors.some(retryableError =>
      errorMessage.includes(retryableError) || errorName.includes(retryableError)
    );
  }

  /**
   * Sync with comprehensive error handling and recovery
   */
  async syncWithRecovery(options: SyncOptions): Promise<SyncResult> {
    try {
      // Check if sync is already running for this date
      const existingSync = await this.getSyncStatus(options.startDate.toFormat('yyyy-MM-dd'));
      if (existingSync && existingSync.sync_status === 'running') {
        const runningSince = new Date(existingSync.started_at);
        const runningDuration = Date.now() - runningSince.getTime();

        if (runningDuration > this.maxSyncDuration) {
          logger.warn(`Sync has been running for ${runningDuration}ms, marking as failed and starting new sync`);
          await this.markSyncAsFailed(existingSync.id, 'Sync timeout - exceeded maximum duration');
        } else {
          throw new Error(`Sync already running for ${options.startDate.toFormat('yyyy-MM-dd')} (started ${runningSince.toISOString()})`);
        }
      }

      // Execute sync with retry logic
      return await this.executeWithRetry(
        () => this.syncPaymentsForDateRange(options),
        `Sync for ${options.startDate.toFormat('yyyy-MM-dd')}`,
        2 // Fewer retries for full sync operations
      );

    } catch (error) {
      logger.error('Sync with recovery failed:', error);

      // Attempt partial recovery if possible
      if (options.syncType !== 'manual') {
        try {
          const errorToPass = error instanceof Error ? error : new Error('Unknown sync error');
          return await this.attemptPartialRecovery(options, errorToPass);
        } catch (recoveryError) {
          logger.error('Partial recovery also failed:', recoveryError);
        }
      }

      throw error;
    }
  }

  /**
   * Attempt partial recovery by syncing smaller chunks
   */
  private async attemptPartialRecovery(options: SyncOptions, originalError: Error): Promise<SyncResult> {
    logger.info('Attempting partial recovery with smaller chunks');

    const { startDate, endDate } = options;
    const totalHours = endDate.diff(startDate, 'hours').hours;

    if (totalHours <= 1) {
      throw new Error('Cannot recover: time range too small for chunking');
    }

    const chunkHours = Math.max(1, Math.floor(totalHours / 4)); // Split into 4 chunks
    const chunks: { start: DateTime; end: DateTime }[] = [];

    let currentStart = startDate;
    while (currentStart < endDate) {
      const currentEnd = currentStart.plus({ hours: chunkHours });
      chunks.push({
        start: currentStart,
        end: currentEnd > endDate ? endDate : currentEnd,
      });
      currentStart = currentEnd;
    }

    logger.info(`Attempting recovery with ${chunks.length} chunks of ${chunkHours} hours each`);

    const aggregatedResult: SyncResult = {
      success: false,
      paymentsProcessed: 0,
      paymentsInserted: 0,
      paymentsUpdated: 0,
      errors: [`Original error: ${originalError.message}`],
      syncDuration: 0,
    };

    let successfulChunks = 0;

    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      try {
        logger.info(`Processing recovery chunk ${i + 1}/${chunks.length}: ${chunk.start.toISO()} to ${chunk.end.toISO()}`);

        const chunkResult = await this.syncPaymentsForDateRange({
          ...options,
          startDate: chunk.start,
          endDate: chunk.end,
          syncType: 'manual', // Mark as manual to avoid recursive recovery
        });

        aggregatedResult.paymentsProcessed += chunkResult.paymentsProcessed;
        aggregatedResult.paymentsInserted += chunkResult.paymentsInserted;
        aggregatedResult.paymentsUpdated += chunkResult.paymentsUpdated;
        successfulChunks++;

      } catch (chunkError) {
        const errorMessage = `Chunk ${i + 1} failed: ${chunkError instanceof Error ? chunkError.message : 'Unknown error'}`;
        aggregatedResult.errors.push(errorMessage);
        logger.error(errorMessage);
      }
    }

    aggregatedResult.success = successfulChunks > 0;

    if (successfulChunks === chunks.length) {
      logger.info('Partial recovery completed successfully - all chunks processed');
    } else {
      logger.warn(`Partial recovery completed with issues - ${successfulChunks}/${chunks.length} chunks successful`);
    }

    return aggregatedResult;
  }

  /**
   * Mark a sync as failed in the database
   */
  private async markSyncAsFailed(syncLogId: string, errorMessage: string): Promise<void> {
    const client = await db.connect();

    try {
      const query = `
        UPDATE stripe_sync_log
        SET sync_status = 'failed', error_message = $1, completed_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `;

      await client.query(query, [errorMessage, syncLogId]);
      logger.info(`Marked sync ${syncLogId} as failed: ${errorMessage}`);
    } catch (error) {
      logger.error('Failed to mark sync as failed:', error);
    } finally {
      client.release();
    }
  }

  /**
   * Utility method for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Health check for sync service
   */
  async healthCheck(): Promise<{
    healthy: boolean;
    stripeApiConnected: boolean;
    databaseConnected: boolean;
    lastSyncStatus?: SyncLogData;
    issues: string[];
  }> {
    const issues: string[] = [];
    let stripeApiConnected = false;
    let databaseConnected = false;

    // Test Stripe API connection
    try {
      stripeApiConnected = await this.stripeAPI.testConnection();
      if (!stripeApiConnected) {
        issues.push('Stripe API connection failed');
      }
    } catch (error) {
      issues.push(`Stripe API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Test database connection
    try {
      const client = await db.connect();
      await client.query('SELECT 1');
      client.release();
      databaseConnected = true;
    } catch (error) {
      issues.push(`Database connection error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Get last sync status
    let lastSyncStatus: SyncLogData | undefined = undefined;
    try {
      const recentSyncs = await this.getRecentSyncHistory(1);
      lastSyncStatus = recentSyncs[0];
    } catch (error) {
      issues.push(`Failed to get sync history: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return {
      healthy: issues.length === 0,
      stripeApiConnected,
      databaseConnected,
      lastSyncStatus,
      issues,
    };
  }
}

export default StripeSyncService;
export { SyncResult, SyncOptions };
