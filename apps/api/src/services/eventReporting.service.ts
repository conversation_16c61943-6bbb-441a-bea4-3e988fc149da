import { WebClient } from '@slack/web-api';
import config from '../config';
import { logger } from '../config/logger';
import { DateTime } from 'luxon';

export interface UserEvent {
  actor: 'Doctor' | 'Patient';
  target: string;
  action: string;
  message: string;
  timestamp: string;
  actor_id: string;
  target_id?: string;
  doctor_name: string; // Added to include doctor's name in the report
}

class EventReportingService {
  private slack: WebClient;

  constructor() {
    this.slack = new WebClient(config.slackEventToken);
  }

  async reportEvent(events: UserEvent[]): Promise<string | null> {
    const slackChannel = config.slackEventReportingChannel;
    if (!slackChannel || !config.slackEventToken) {
      logger.info('No Slack channel or token configured for event reporting');
      return null;
    }

    const sortedEvents = [...events].sort((a, b) => Date.parse(a.timestamp) - Date.parse(b.timestamp));

    const eventLines = sortedEvents
      .map((e, idx) => {
        const targetInfo = e.target ? ` Target: ${e.target}` : e.target_id ? ` Target: ${e.target_id}` : '';
        return `${idx + 1}. [${DateTime.fromISO(e.timestamp).toFormat('h:mm:ss a, dd MMM yyyy')}] ${e.message} | ${targetInfo}`;
      })
      .join('\n');

    // const extraText = `*Session Start Time*: ${DateTime.fromISO(sortedEvents[0].timestamp).toFormat('h:mm a, dd MMM yyyy')}
    //     *Session End Time*: ${DateTime.fromISO(sortedEvents[sortedEvents.length - 1].timestamp).toFormat('h:mm a, dd MMM yyyy')}
    //     *Duration*: ${DateTime.fromISO(sortedEvents[sortedEvents.length - 1].timestamp)
    //       .diff(DateTime.fromISO(sortedEvents[0].timestamp), ['hours', 'minutes'])
    //       .toFormat('hh:mm')}
    //     *Total Actions*: ${sortedEvents.length}`;

    const messageText = `
        🔔 *Doctor Activity Report*
        *Doctor*: ${sortedEvents[0].doctor_name}
        
        -------------------------------------------------------------------------
        *Timeline of Actions:*
        -------------------------------------------------------------------------\n
        ${eventLines}
    `.trim();

    try {
      const response = await this.slack.chat.postMessage({
        channel: slackChannel,
        text: messageText,
        username: 'Event Reporter',
        icon_emoji: ':robot_face:',
      });

      logger.info(`Event report sent to Slack channel ${slackChannel}: ${response.ts}`);

      if (response.ts) {
        // will store the slack timestamp later on
      }
      return response.ts || 'Successfully reported';
    } catch (error) {
      logger.error('Error reporting event:', error);
      return null;
    }
  }

  async sendTestNotification(): Promise<void> {
    const slackChannel = config.slackEventReportingChannel;

    if (!slackChannel || !config.slackEventToken) {
      throw new Error('Slack event reporting channel or token not configured');
    }

    const now = DateTime.now().setZone('Australia/Sydney');
    const timestamp = now.toFormat('h:mm a, dd MMM yyyy');

    const messageText = `🧪 *TEST EVENT REPORTING NOTIFICATION*
        This is a test notification sent at ${timestamp}
    `;

    try {
      await this.slack.chat.postMessage({
        channel: slackChannel,
        text: messageText,
        username: 'Event Reporter',
        icon_emoji: ':robot_face:',
      });
    } catch (error) {
      logger.error('Error sending test notification:', error);
    }
  }
}

export const eventReportingService = new EventReportingService();
