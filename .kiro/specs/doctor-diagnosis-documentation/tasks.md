# Implementation Plan

- [x] 1. Database schema update and backend API modifications
  - Add diagnosis column to treatmentplan table with proper migration
  - Update backend API endpoints to handle diagnosis field in treatment plan submissions
  - Modify database queries to include diagnosis column in SELECT and INSERT operations
  - Add proper validation and sanitization for diagnosis field in backend
  - _Requirements: 1.1.1, 1.1.2, 1.1.3_

- [x] 2. Update TypeScript type definitions
  - Add diagnosis field to PatientTreatmentPlan interface in apps/web/src/types/index.ts
  - Update TreatmentPlanWithPatient interface to include diagnosis field
  - Ensure type safety across all components that use treatment plan data
  - _Requirements: 1.1.4, 3.4_

- [x] 3. Implement diagnosis field in treatment plan form
  - Add diagnosis TextField component to apps/web/src/components/doc-portal/patient-form.tsx
  - Implement handleDiagnosisChange function to update PatientTreatmentPlan state
  - Add character limit enforcement (2000 characters) with real-time counter
  - Position diagnosis field logically within existing form structure
  - Apply consistent styling with existing form fields
  - _Requirements: 1.1, 1.2, 3.1, 3.2_

- [x] 4. Create diagnosis display component for email confirmation
  - Create new component apps/web/src/components/doc-portal/emailConfirmation/diagnosis.tsx
  - Implement collapsible section with appropriate patient-friendly messaging
  - Add conditional rendering (only show when diagnosis exists)
  - Style diagnosis display with proper formatting and visual hierarchy
  - Include supportive messaging explaining the diagnosis purpose
  - _Requirements: 2.1, 2.2, 2.3, 4.1, 4.2, 4.3, 4.4, 4.5_

- [x] 5. Integrate diagnosis component into email preview
  - Import and add EmailDiagnosis component to apps/web/src/components/doc-portal/emailConfirmation/email-preview.tsx
  - Position diagnosis section appropriately in email layout (after intro, before treatment plan)
  - Ensure proper responsive behavior on mobile devices
  - Test integration with existing email confirmation workflow
  - _Requirements: 2.4, 3.3, 4.1_

- [x] 6. Update form submission logic
  - Modify treatment plan submission functions to include diagnosis field
  - Update API calls in email-preview.tsx to send diagnosis data
  - Ensure diagnosis is properly included in both regular and cancelled submissions
  - Add proper error handling for diagnosis field submission
  - _Requirements: 1.3, 3.4, 5.4_

- [x] 7. Implement logging and audit trail functionality
  - Add logging for diagnosis field creation and updates
  - Include diagnosis information in treatment plan audit logs
  - Implement version history tracking for diagnosis changes
  - Add access logging for diagnosis data retrieval
  - _Requirements: 5.1, 5.2, 5.3, 5.5_

- [ ] 8. Add comprehensive testing
  - Write unit tests for diagnosis field form component behavior
  - Create tests for diagnosis display component rendering and messaging
  - Add integration tests for form submission with diagnosis data
  - Test database operations for diagnosis storage and retrieval
  - Implement end-to-end tests for complete diagnosis workflow
  - _Requirements: All requirements validation_
