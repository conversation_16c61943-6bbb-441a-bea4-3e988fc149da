# Design Document

## Overview

This design adds a diagnosis documentation field to the existing doctor treatment plan form, allowing doctors to record patient diagnoses that will be stored in the database and displayed to patients with appropriate messaging. The enhancement integrates seamlessly with the current treatment plan workflow while providing clear communication to patients about their medical condition.

## Architecture

### Current System Analysis

The existing system implements treatment plan functionality through:
- Treatment plan form in `apps/web/src/components/doc-portal/patient-form.tsx`
- Email confirmation preview in `apps/web/src/components/doc-portal/emailConfirmation/`
- Database storage in `treatmentplan` table
- Patient treatment plan context via `usePatient()` hook
- Type definitions in `PatientTreatmentPlan` interface

### Enhanced Architecture

The enhanced system extends the current architecture by:
- Adding a diagnosis field to the treatment plan form
- Extending the `treatmentplan` database table with a `diagnosis` column
- Adding diagnosis display to the email confirmation/patient summary
- Updating the `PatientTreatmentPlan` type to include diagnosis
- Implementing appropriate messaging for patient communication

```mermaid
graph TD
    A[Doctor Treatment Plan Form] --> B[Diagnosis Field]
    B --> C[PatientTreatmentPlan State]
    C --> D[Database Storage]
    D --> E[treatmentplan.diagnosis column]
    C --> F[Email Confirmation Preview]
    F --> G[Patient Summary Display]
    G --> H[Diagnosis Section with Messaging]
```

## Components and Interfaces

### 1. Database Schema Extension

**Purpose:** Add diagnosis storage to the existing treatmentplan table.

**Schema Change:**
```sql
ALTER TABLE treatmentplan 
ADD COLUMN diagnosis TEXT;

COMMENT ON COLUMN treatmentplan.diagnosis IS 'Patient diagnosis documented by the doctor during consultation';
```

**Integration:**
- Nullable field to support existing records
- TEXT type to accommodate various diagnosis lengths
- Indexed for efficient querying if needed

### 2. Type System Updates

**Purpose:** Extend existing types to include diagnosis information.

**PatientTreatmentPlan Extension:**
```typescript
export type PatientTreatmentPlan = {
  patient?: PatientData;
  email?: {
    introMessage?: { intro: string; conclusion: string };
    listItemText?: ListItemTextTpProps;
    listTitle?: ListTitleTpProps;
    otherTreatment?: OtherTreatmentTpProps;
    checkedSativa?: string[];
    checkedIndica?: string[];
    checkedHybrid?: string[];
  };
  treatmentPlan?: {
    // ... existing fields
  };
  outcome?: ConsultationOutCome;
  drNotes?: string;
  diagnosis?: string; // New field
  mentalHealthSupportingDocumentation?: "Yes" | "No";
  date?: string;
  drId?: string;
  drAphraNumber?: string;
  drName?: string;
  "22"?: TreatmentPlan;
  "29"?: TreatmentPlan;
};
```

### 3. Treatment Plan Form Enhancement

**Purpose:** Add diagnosis input field to the existing treatment plan form.

**Component Location:** `apps/web/src/components/doc-portal/patient-form.tsx`

**Implementation:**
```typescript
// Add to form state management
const handleDiagnosisChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
  const { value } = e.target;
  setPatientTreatmentPlan((prev) => ({
    ...prev,
    diagnosis: value
  }));
};

// Form field implementation
<TextField
  multiline
  fullWidth
  rows={3}
  label="Patient Diagnosis"
  placeholder="Enter patient diagnosis..."
  value={patientTreatmentPlan?.diagnosis || ''}
  onChange={handleDiagnosisChange}
  sx={{
    '& .MuiInputBase-root': {
      borderRadius: '5px',
      backgroundColor: 'white',
      fontSize: '14px'
    }
  }}
  inputProps={{
    maxLength: 2000
  }}
  helperText={`${(patientTreatmentPlan?.diagnosis || '').length}/2000 characters`}
/>
```

### 4. Email Confirmation Enhancement

**Purpose:** Display diagnosis in patient email confirmation with appropriate messaging.

**Component Location:** Create new component `apps/web/src/components/doc-portal/emailConfirmation/diagnosis.tsx`

**Implementation:**
```typescript
import React, { useState } from "react";
import { Box, Typography, useMediaQuery, IconButton, Collapse } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { useTheme } from "@mui/material/styles";
import { usePatient } from "../../../hooks/patient-provider";
import ArrowRightIcon from '@mui/icons-material/ArrowRight';

const EmailDiagnosis: React.FC = () => {
    const { patientTreatmentPlan, selectedPatient } = usePatient();
    const theme = useTheme();
    const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));
    const [open, setOpen] = useState(false);

    const handleToggle = () => {
        setOpen(!open);
    };

    // Don't render if no diagnosis
    if (!patientTreatmentPlan?.diagnosis?.trim()) {
        return null;
    }

    return (
        <Grid container sx={{ width: '100%', fontSize: '12px' }} direction={'column'}>
            <Grid container alignItems={'center'}>
                <IconButton
                    onClick={handleToggle}
                    sx={{
                        transform: open ? 'rotate(90deg)' : 'rotate(0deg)',
                        width: '20px',
                        height: '20px',
                        mr: 2,
                        transition: 'transform 0.3s'
                    }}
                >
                    <ArrowRightIcon sx={{ color: 'black' }} />
                </IconButton>
                <span style={{ fontWeight: 'bold', fontSize: '24px' }}>Your Diagnosis</span>
            </Grid>

            <Collapse in={open} timeout={10}>
                <Grid sx={{ width: '100%', fontSize: '14px', mt: 2 }}>
                    <Typography sx={{ mb: 2, fontWeight: 'medium' }}>
                        Based on your consultation, your doctor has provided the following diagnosis:
                    </Typography>
                    
                    <Box sx={{ 
                        p: 2, 
                        backgroundColor: '#f8f9fa', 
                        borderRadius: '8px',
                        border: '1px solid #e9ecef'
                    }}>
                        <Typography sx={{ fontSize: '14px', lineHeight: 1.6 }}>
                            {patientTreatmentPlan.diagnosis}
                        </Typography>
                    </Box>

                    <Typography sx={{ mt: 2, fontSize: '12px', color: '#6c757d' }}>
                        This diagnosis helps guide your treatment plan and ensures you receive the most appropriate care for your condition.
                        If you have any questions about your diagnosis, please don't hesitate to contact your healthcare provider.
                    </Typography>
                </Grid>
            </Collapse>
        </Grid>
    );
};

export default EmailDiagnosis;
```

### 5. Email Preview Integration

**Purpose:** Include diagnosis component in the email confirmation preview.

**Component Location:** `apps/web/src/components/doc-portal/emailConfirmation/email-preview.tsx`

**Integration:**
```typescript
// Add import
import EmailDiagnosis from "./diagnosis";

// Add to the email preview layout (after ConsultationMessage, before EmailTreatmentPlan)
<ConsultationMessage />
<EmailDiagnosis />
<EmailTreatmentPlan />
<EmailStrainAdvice />
```

## Data Models

### Database Schema

```sql
-- Migration script
ALTER TABLE treatmentplan 
ADD COLUMN diagnosis TEXT;

-- Add index for efficient querying (optional)
CREATE INDEX idx_treatmentplan_diagnosis ON treatmentplan(diagnosis) 
WHERE diagnosis IS NOT NULL;

-- Add comment for documentation
COMMENT ON COLUMN treatmentplan.diagnosis IS 'Patient diagnosis documented by the doctor during consultation';
```

### API Integration

**Backend Changes Required:**
- Update treatment plan API endpoints to include diagnosis field
- Modify database queries to include diagnosis column
- Update validation to handle diagnosis field

**API Endpoint Updates:**
```typescript
// In treatment plan submission
interface TreatmentPlanSubmission {
  // ... existing fields
  diagnosis?: string;
}

// Database insertion/update queries should include diagnosis field
const insertQuery = `
  INSERT INTO treatmentplan (
    "patientID", "drId", "consultationId", "outcome", 
    "drNotes", "diagnosis", /* other fields */
  ) VALUES ($1, $2, $3, $4, $5, $6, /* other values */)
`;
```

## Error Handling

### Form Validation

1. **Character Limit Enforcement:**
   - Maximum 2000 characters for diagnosis field
   - Real-time character count display
   - Graceful handling of limit exceeded

2. **Optional Field Handling:**
   - Form submission allowed with empty diagnosis
   - Clear indication that field is optional
   - No validation errors for empty diagnosis

### Database Operations

1. **Migration Safety:**
   - Nullable column addition for backward compatibility
   - Existing records unaffected
   - Rollback capability if needed

2. **Storage Errors:**
   - Graceful handling of database write failures
   - User feedback for save errors
   - Retry mechanisms where appropriate

## Testing Strategy

### Unit Tests

1. **Form Component Tests:**
   ```typescript
   describe('Diagnosis Field', () => {
     test('should update diagnosis in treatment plan state');
     test('should enforce character limit');
     test('should display character count');
     test('should handle empty diagnosis gracefully');
   });
   ```

2. **Email Component Tests:**
   ```typescript
   describe('Email Diagnosis Component', () => {
     test('should render diagnosis when present');
     test('should not render when diagnosis is empty');
     test('should display appropriate messaging');
     test('should handle long diagnosis text');
   });
   ```

### Integration Tests

1. **Form Submission:**
   - Test diagnosis included in treatment plan submission
   - Verify database storage of diagnosis
   - Test form submission with and without diagnosis

2. **Email Preview:**
   - Test diagnosis display in email confirmation
   - Verify messaging and formatting
   - Test responsive design on mobile devices

### Database Tests

1. **Schema Migration:**
   - Test column addition
   - Verify existing data integrity
   - Test rollback scenarios

2. **Data Operations:**
   - Test diagnosis storage and retrieval
   - Verify query performance
   - Test with various diagnosis lengths

## Implementation Considerations

### User Experience

- **Form Flow:** Diagnosis field positioned logically in treatment plan form
- **Visual Design:** Consistent with existing form styling
- **Mobile Responsiveness:** Proper display on all device sizes
- **Accessibility:** Proper labels and ARIA attributes

### Performance

- **Database Impact:** Minimal impact from adding nullable text column
- **Query Optimization:** Index on diagnosis field if search functionality needed
- **Form Performance:** No significant impact on form rendering

### Security

- **Input Sanitization:** Proper sanitization of diagnosis text input
- **Data Privacy:** Diagnosis treated as sensitive medical information
- **Access Control:** Same access controls as other treatment plan data

### Maintenance

- **Code Organization:** Diagnosis component follows existing patterns
- **Type Safety:** Full TypeScript integration
- **Documentation:** Clear comments and documentation
- **Backward Compatibility:** Existing functionality unaffected

## Migration Strategy

### Phase 1: Database Schema Update
- Add diagnosis column to treatmentplan table
- Test migration on staging environment
- Prepare rollback scripts

### Phase 2: Backend API Updates
- Update treatment plan endpoints to handle diagnosis
- Modify database queries to include diagnosis field
- Update API documentation

### Phase 3: Frontend Implementation
- Add diagnosis field to treatment plan form
- Create diagnosis display component
- Update type definitions

### Phase 4: Testing & Deployment
- Comprehensive testing across all components
- User acceptance testing
- Gradual rollout with monitoring

### Phase 5: Documentation & Training
- Update user documentation
- Provide training materials for doctors
- Monitor usage and gather feedback