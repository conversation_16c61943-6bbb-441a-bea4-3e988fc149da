# Requirements Document

## Introduction

This feature enhances the admin Treatment Plan Requests interface to provide better visibility into rejected requests by displaying the doctor name who processed the rejection and the reason for rejection directly in the main table view. Currently, rejected requests show "N/A" for the doctor name and the rejection reason is only visible in the detailed modal view, making it difficult for administrators to quickly understand rejection patterns and decisions.

## Requirements

### Requirement 1

**User Story:** As an administrator, I want to see which doctor rejected a treatment plan request directly in the main table view, so that I can quickly identify who made the decision without opening detailed modals.

#### Acceptance Criteria

1. WHEN viewing processed requests in the admin interface THEN the system SHALL display the actual doctor name who processed rejected requests instead of "N/A"
2. WHEN a request is rejected by a doctor THEN the system SHALL store and retrieve the doctor's name associated with the rejection
3. WHEN displaying the doctor column for rejected requests THEN the system SHALL show the reviewing doctor's name consistently with approved requests
4. WHEN the doctor name is not available THEN the system SHALL display "Unknown" instead of "N/A" for clarity
5. WHEN filtering by rejected requests THEN all rejected requests SHALL show the processing doctor's name if available

### Requirement 2

**User Story:** As an administrator, I want to see the rejection reason directly in the main table view, so that I can quickly understand why requests were rejected without opening individual detail modals.

#### Acceptance Criteria

1. WHEN viewing rejected requests in the main table THEN the system SHALL display a summary of the rejection reason in a dedicated column or tooltip
2. WHEN the rejection reason is longer than the display space THEN the system SHALL show a truncated version with an option to view the full reason
3. WHEN hovering over a truncated rejection reason THEN the system SHALL display the full reason in a tooltip
4. WHEN no rejection reason is provided THEN the system SHALL display "No reason provided" 
5. WHEN viewing approved requests THEN the rejection reason column SHALL show "N/A" or be hidden appropriately

### Requirement 3

**User Story:** As an administrator, I want the rejection information to be clearly formatted and easily scannable, so that I can efficiently review rejection patterns and make informed decisions.

#### Acceptance Criteria

1. WHEN displaying rejection reasons THEN the system SHALL format them in a consistent, readable manner
2. WHEN showing doctor names for rejections THEN the system SHALL use consistent formatting with other doctor name displays
3. WHEN viewing the processed requests table THEN rejection-specific information SHALL be visually distinct but not overwhelming
4. WHEN sorting or filtering requests THEN the rejection information SHALL be included in the sorting/filtering logic
5. WHEN exporting or printing the requests table THEN rejection information SHALL be included in the output

### Requirement 4

**User Story:** As an administrator, I want to ensure that rejection information is properly tracked and auditable, so that we maintain proper records of all administrative decisions.

#### Acceptance Criteria

1. WHEN a doctor rejects a request THEN the system SHALL properly associate the doctor's identity with the rejection
2. WHEN retrieving processed requests THEN the system SHALL include complete rejection information in the API response
3. WHEN displaying rejection information THEN the system SHALL ensure data consistency between the table view and detail modal
4. WHEN a rejection is processed THEN the system SHALL log the doctor name and reason for audit purposes
5. WHEN querying rejection data THEN the system SHALL provide accurate and complete information for reporting

### Requirement 5

**User Story:** As an administrator, I want the enhanced rejection display to work seamlessly with existing filtering and pagination features, so that the interface remains efficient and user-friendly.

#### Acceptance Criteria

1. WHEN filtering by rejected status THEN the enhanced rejection information SHALL be displayed for all filtered results
2. WHEN paginating through rejected requests THEN the rejection information SHALL be consistently displayed across all pages
3. WHEN using the type filter with rejected requests THEN the rejection information SHALL be shown regardless of request type
4. WHEN the table layout adjusts for different screen sizes THEN the rejection information SHALL remain accessible and readable
5. WHEN combining multiple filters THEN the rejection information display SHALL remain consistent and accurate