# Design Document

## Overview

This design enhances the admin Treatment Plan Requests interface by improving the display of rejection information. The solution involves both frontend UI improvements and potential backend data retrieval enhancements to ensure rejected requests show the processing doctor's name and rejection reason directly in the main table view.

## Architecture

### Frontend Components
- **ProcessedRequestsTab.tsx**: Main table component that displays processed requests
- **RequestDetailsModal.tsx**: Modal component for detailed request view (reference for data consistency)
- **admin-requests.service.ts**: Service layer for API communication

### Backend Integration
- **Admin Requests API**: Existing API endpoints that provide processed request data
- **Database**: Treatment plan requests table with rejection tracking

### Data Flow
1. Admin loads processed requests page
2. Frontend calls admin requests API with filters
3. Backend retrieves processed requests with complete doctor and rejection information
4. Frontend displays enhanced table with rejection details
5. User can view additional details in modal if needed

## Components and Interfaces

### Enhanced AdminRequest Interface
```typescript
export interface AdminRequest {
  id: string;
  type: 'thc_increase' | 'extend_tp';
  patient_id: string;
  email: string;
  patient_name: string;
  total_score: number;
  max_score: number;
  status: 'pending' | 'submitted' | 'approved' | 'rejected';
  created_at: string;
  reviewed_at?: string;
  reviewed_by?: string;
  review_notes?: string;
  doctor_name?: string; // This should be populated for all processed requests
  questionnaire_data?: any;
}
```

### ProcessedRequestsTab Enhancements

#### Table Structure Updates
- **Doctor Column**: Enhanced to always show the processing doctor's name
- **New Rejection Reason Column**: Added for rejected requests (conditionally displayed)
- **Responsive Design**: Ensure new information fits well on different screen sizes

#### UI Components
```typescript
// Enhanced table header
const tableHeaders = [
  'Patient',
  'Request Type', 
  'Status',
  'Risk Score',
  'Submitted',
  'Processed',
  'Doctor',
  'Rejection Reason', // New column, shown conditionally
  'Actions'
];

// Rejection reason display component
const RejectionReasonCell = ({ reason, maxLength = 50 }) => {
  if (!reason) return <Typography variant="body2" color="textSecondary">N/A</Typography>;
  
  const truncated = reason.length > maxLength;
  const displayText = truncated ? `${reason.substring(0, maxLength)}...` : reason;
  
  return (
    <Tooltip title={truncated ? reason : ''} arrow>
      <Typography variant="body2" sx={{ cursor: truncated ? 'help' : 'default' }}>
        {displayText}
      </Typography>
    </Tooltip>
  );
};
```

### Service Layer Enhancements

#### API Request Modifications
```typescript
// Enhanced getProcessedRequests method
async getProcessedRequests(
  status?: 'approved' | 'rejected',
  type?: 'thc_increase' | 'extend_tp',
  limit = 50,
  page = 1
): Promise<{ requests: AdminRequest[] }> {
  // Ensure API returns complete doctor information for all processed requests
  const params = new URLSearchParams();
  if (status) params.append('status', status);
  if (type) params.append('type', type);
  params.append('limit', limit.toString());
  params.append('page', page.toString());
  params.append('include_doctor_details', 'true'); // Request complete doctor info

  const response = await axios.get(
    `${API_BASE_URL}/admin/v1.0/requests/processed?${params}`,
    { withCredentials: true }
  );
  return response.data.data;
}
```

## Data Models

### Request Processing Data
```typescript
interface ProcessedRequestData {
  id: string;
  status: 'approved' | 'rejected';
  reviewed_at: string;
  reviewed_by: string; // Doctor ID
  doctor_name: string; // Doctor full name - must be populated
  review_notes?: string; // Rejection reason for rejected requests
}
```

### Display Data Structure
```typescript
interface EnhancedDisplayRequest extends AdminRequest {
  // Computed properties for display
  displayDoctorName: string; // Never "N/A", always shows actual name or "Unknown"
  displayRejectionReason?: string; // Formatted rejection reason for display
  hasRejectionReason: boolean; // Flag for conditional column display
}
```

## Error Handling

### Missing Doctor Information
- **Fallback Display**: Show "Unknown Doctor" instead of "N/A"
- **Logging**: Log instances where doctor information is missing for investigation
- **API Validation**: Ensure backend provides doctor information for all processed requests

### Missing Rejection Reasons
- **Default Message**: Show "No reason provided" for rejected requests without reasons
- **Validation**: Encourage (but don't require) rejection reasons in the processing workflow

### API Failures
- **Graceful Degradation**: Show existing information if enhanced data fails to load
- **Error Messages**: Clear user feedback when data cannot be retrieved
- **Retry Logic**: Automatic retry for transient failures

## Testing Strategy

### Unit Tests
- **Component Rendering**: Test ProcessedRequestsTab with various request states
- **Data Formatting**: Test rejection reason truncation and tooltip functionality
- **Service Layer**: Test API calls with enhanced parameters

### Integration Tests
- **End-to-End Flow**: Test complete workflow from API to UI display
- **Filter Interactions**: Test rejection information display with various filters
- **Modal Consistency**: Ensure data consistency between table and modal views

### User Acceptance Tests
- **Admin Workflow**: Test typical admin review scenarios
- **Performance**: Ensure enhanced display doesn't impact table performance
- **Responsive Design**: Test on various screen sizes and devices

### Test Scenarios
```typescript
describe('Enhanced Rejection Display', () => {
  test('shows doctor name for rejected requests', () => {
    // Test that rejected requests display actual doctor names
  });
  
  test('displays rejection reason with truncation', () => {
    // Test rejection reason display and tooltip functionality
  });
  
  test('handles missing rejection data gracefully', () => {
    // Test fallback behavior for incomplete data
  });
  
  test('maintains consistency with detail modal', () => {
    // Test that table and modal show same information
  });
});
```

## Implementation Considerations

### Performance
- **Data Loading**: Ensure enhanced data doesn't significantly impact load times
- **Table Rendering**: Optimize rendering of additional columns
- **Pagination**: Maintain efficient pagination with enhanced data

### Accessibility
- **Screen Readers**: Ensure rejection information is accessible
- **Keyboard Navigation**: Maintain keyboard accessibility for new elements
- **Color Contrast**: Ensure rejection information meets accessibility standards

### Responsive Design
- **Mobile View**: Consider how rejection information displays on smaller screens
- **Column Priority**: Determine which columns to hide/show on different screen sizes
- **Horizontal Scrolling**: Manage table width with additional columns

### Data Consistency
- **API Alignment**: Ensure frontend expectations match backend data structure
- **Cache Management**: Handle caching of enhanced request data appropriately
- **Real-time Updates**: Consider how rejection information updates in real-time scenarios