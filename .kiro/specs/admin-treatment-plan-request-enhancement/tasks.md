# Implementation Plan

- [ ] 1. Investigate and fix doctor name display for rejected requests
  - Analyze why `doctor_name` field shows "N/A" for rejected requests in the current data
  - Check if the issue is in the backend API response or frontend data handling
  - Implement fix to ensure `doctor_name` is properly populated for all processed requests
  - Add fallback logic to display "Unknown Doctor" instead of "N/A" when doctor name is unavailable
  - _Requirements: 1.1, 1.3, 1.4_

- [ ] 2. Add rejection reason column to ProcessedRequestsTab table
  - Modify the table header to include a "Rejection Reason" column that appears conditionally for rejected requests
  - Create a RejectionReasonCell component that handles text truncation and tooltip display
  - Implement logic to show/hide the rejection reason column based on filter selection
  - Ensure the column integrates well with existing table layout and responsive design
  - _Requirements: 2.1, 2.2, 2.3, 2.4_

- [ ] 3. Implement rejection reason display logic and formatting
  - Create utility functions for formatting and truncating rejection reasons
  - Implement tooltip functionality for displaying full rejection reasons on hover
  - Add proper handling for missing rejection reasons with "No reason provided" fallback
  - Ensure consistent formatting between table view and detail modal
  - _Requirements: 2.2, 2.3, 2.4, 3.1, 3.2_

- [ ] 4. Update table layout and responsive design for new column
  - Adjust table column widths to accommodate the new rejection reason column
  - Implement responsive behavior for the rejection reason column on smaller screens
  - Ensure table remains usable and readable with the additional column
  - Test horizontal scrolling behavior if needed for mobile devices
  - _Requirements: 3.3, 5.4_

- [ ] 5. Enhance filtering and display logic for rejection information
  - Update filter logic to properly handle rejection information display
  - Ensure rejection reason column shows/hides appropriately based on status filter
  - Implement proper sorting functionality for the doctor name and rejection reason columns
  - Verify pagination works correctly with enhanced rejection information
  - _Requirements: 5.1, 5.2, 5.3, 5.5_

-
- [ ] 6. Update admin requests service if needed for enhanced data retrieval
  - Review current API calls to ensure they request complete doctor information
  - Add any necessary parameters to API requests for enhanced rejection data
  - Implement proper error handling in service layer for missing data
  - Ensure service layer provides consistent data structure for enhanced display
  - _Requirements: 4.1, 4.2, 4.5_

- [ ] 7. Perform final testing and validation of enhanced rejection display
  - Conduct comprehensive testing of all rejection display scenarios
  - Validate that existing functionality remains unaffected
  - Test performance impact of enhanced data display
  - Verify accessibility compliance for new rejection information elements
  - _Requirements: 1.1, 2.1, 3.3, 4.4, 5.5_