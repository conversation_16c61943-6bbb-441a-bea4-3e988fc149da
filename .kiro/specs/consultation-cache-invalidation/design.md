# Design Document: Consultation Cache Invalidation

## Overview

This design document outlines a simple implementation of a cache invalidation system for the `getPatientsRedis` method in the doctor controller. The current implementation caches patient data in memory but lacks a mechanism to detect when new consultation records are added to the database, which can lead to stale data being served to clients. The proposed solution will implement a straightforward cache invalidation system that checks for new consultation records and refreshes the cache when necessary.

## Architecture

The cache invalidation system will be implemented directly within the existing `getPatientsRedis` method. It will use a timestamp-based approach to detect changes in the consultation table. The system will store the timestamp of the last cache refresh, and on subsequent requests, it will check if any new records have been added since that timestamp.

### High-Level Flow

1. When a request is made to `getPatientsRedis`:
   - If the cache is empty, fetch data from the database
   - If the cache is not empty, check if any new consultation records have been added since the last cache refresh
   - If new records exist, refresh the cache
   - Otherwise, return the cached data

2. When refreshing the cache:
   - Fetch the latest data from the database
   - Update the cache
   - Store the current timestamp as the last refresh time

## Data Storage

We will add two new variables to store the cache state:

```typescript
// Existing variable for storing patient data
let patientRedis: PatientData[] = [];

// New variable to track the last cache refresh time
let lastCacheRefreshTime: Date | null = null;
```

## Error Handling

1. **Database Connection Errors**: If the database connection fails during cache refresh, the system will log the error and return the existing cached data if available.

2. **Query Errors**: If the query to check for new consultation records fails, the system will log the error and assume the cache needs to be refreshed.

## Database Considerations

To optimize the performance of the cache invalidation mechanism, we need to ensure that the `createdAt` column in the consultation table is properly indexed. This will allow for efficient querying of new records based on the timestamp.

```sql
CREATE INDEX IF NOT EXISTS idx_consultation_createdat ON consultation ("createdAt");
```

## Implementation Details

### Cache Invalidation Logic

The cache invalidation logic will be based on a simple check:

1. If there are any consultation records with a `createdAt` timestamp newer than the `lastCacheRefreshTime`, the cache will be invalidated.

### Cache Check Query

The query to check for new consultation records will be simple and efficient:

```sql
SELECT COUNT(*) AS new_rows 
FROM consultation 
WHERE "createdAt" > $1;
```

Where `$1` is the `lastCacheRefreshTime`.

## Diagrams

### Cache Invalidation Flow

```mermaid
flowchart TD
    A[Request to getPatientsRedis] --> B{Cache Empty?}
    B -->|Yes| C[Fetch Data from DB]
    B -->|No| D{Check for New Records}
    D -->|New Records Exist| C
    D -->|No New Records| F[Return Cached Data]
    C --> G[Update Cache]
    G --> H[Store Current Timestamp]
    H --> J[Return Fresh Data]
```