# Requirements Document

## Introduction

The current implementation of the `getPatientsRedis` method in the doctor controller caches patient data in memory but lacks a mechanism to detect when new consultation records are added to the database. This can lead to stale data being served to clients. This feature will implement a simple cache invalidation system that checks for new consultation records and refreshes the cache when necessary.

## Requirements

### Requirement 1

**User Story:** As a doctor using the patient portal, I want to always see the most up-to-date patient consultation data, so that I can make informed decisions based on the latest information.

#### Acceptance Criteria

1. WHEN a new consultation record is added to the database THEN the system SHALL detect this change and invalidate the cache
2. WHEN the cache is invalidated THEN the system SHALL fetch fresh data from the database on the next request
3. WHEN the cache is empty THEN the system SHALL fetch data from the database
4. WHEN checking for new records THEN the system SHALL use a simple and efficient query

### Requirement 2

**User Story:** As a system administrator, I want the cache invalidation mechanism to be simple and reliable, so that it doesn't negatively impact the system's response time.

#### Acceptance Criteria

1. WHEN checking for new consultation records THEN the system SHALL use a timestamp-based approach
2. WHEN the cache is valid THEN the system SHALL return cached data without querying the database
3. IF the database query fails during cache refresh THEN the system SHALL log the error and return the existing cached data
4. WHEN implementing the cache invalidation system THEN the system SHALL store the timestamp of the last cache refresh