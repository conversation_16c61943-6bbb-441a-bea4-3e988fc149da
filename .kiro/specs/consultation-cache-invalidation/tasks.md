# Implementation Plan

- [ ] 1. Set up database index for efficient cache invalidation
  - Create an index on the consultation table's createdAt column for efficient timestamp-based queries
  - Verify the index is properly created and functioning
  - _Requirements: 2.1_

- [ ] 2. Add cache state tracking variables
  - Add lastCacheRefreshTime variable to track when the cache was last refreshed
  - Ensure the variable is properly initialized
  - _Requirements: 2.4_

- [ ] 3. Implement cache invalidation check
  - [ ] 3.1 Create function to check for new consultation records
    - Implement a simple query to count new records since last refresh
    - Use the createdAt column with the index for efficient querying
    - _Requirements: 1.1, 1.4, 2.1_

  - [ ] 3.2 Add error handling for the check
    - Implement try-catch block for the database query
    - Log errors appropriately
    - Return cached data on error if available
    - _Requirements: 2.3_

- [ ] 4. Update getPatientsRedis method
  - [ ] 4.1 Modify the method to check for cache invalidation
    - Add condition to check if cache is empty
    - Add condition to check for new records if cache is not empty
    - Update the cache refresh logic to store the current timestamp
    - _Requirements: 1.2, 1.3, 2.2_

  - [ ] 4.2 Implement error handling for cache operations
    - Add try-catch blocks for database operations
    - Return cached data on error if available
    - Log appropriate error messages
    - _Requirements: 2.3_

- [ ] 5. Write tests
  - [ ] 5.1 Create unit tests for cache invalidation logic
    - Test empty cache scenario
    - Test new records scenario
    - Test no new records scenario
    - Test error handling
    - _Requirements: 1.1, 1.2, 2.3_

  - [ ] 5.2 Create integration tests
    - Test integration with database
    - Test cache refresh on new consultation records
    - _Requirements: 1.1, 1.4, 2.1_

- [ ] 6. Documentation and cleanup
  - [ ] 6.1 Add code documentation
    - Add comments to explain the cache invalidation logic
    - Document the database index requirement
    - _Requirements: 2.1_