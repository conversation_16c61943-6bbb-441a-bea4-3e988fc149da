# Design Document

## Overview

The Stripe Sales Reporting system will be implemented as a service-oriented architecture that integrates with the existing codebase patterns. The system will consist of a dedicated reporting service, database storage layer, API endpoints for cron job triggers, and enhanced webhook processing. The design follows the established patterns in the codebase, particularly mirroring the successful `availabilityReporting.service.ts` implementation.

## Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Cron Jobs     │───▶│   API Endpoints  │───▶│ Sales Service   │
│ (Server Level)  │    │  /sales/trigger  │    │   (Business     │
└─────────────────┘    └──────────────────┘    │    Logic)       │
                                               └─────────────────┘
                                                        │
┌─────────────────┐    ┌──────────────────┐           │
│ Stripe Webhooks │───▶│ Enhanced Webhook │           │
│   (External)    │    │    Handler       │           │
└─────────────────┘    └──────────────────┘           │
                                │                      │
                                ▼                      ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   Database       │    │ Slack API       │
                       │ (stripe_payments)│    │ (Formatted      │
                       └──────────────────┘    │  Reports)       │
                                               └─────────────────┘
```

### Component Integration

The system integrates with existing components:
- **Database**: Uses existing `db.ts` connection pool
- **Configuration**: Extends `config/index.ts` with new Slack channels
- **Logging**: Uses existing `logger.ts` for consistent logging
- **Error Handling**: Follows existing `catchAll` and `ApiError` patterns
- **Webhook Processing**: Enhances existing `setZohoInvoicePaymentStatus` handler

## Components and Interfaces

### 1. Sales Reporting Service

**File**: `apps/api/src/services/salesReporting.service.ts`

```typescript
interface DailySalesData {
  date: string;
  totalPayments: number;
  successfulPayments: number;
  failedPayments: number;
  totalAmountCents: number;
  customers: CustomerPayment[];
}

interface WeeklySalesData extends DailySalesData {
  weekStart: string;
  weekEnd: string;
  weekNumber: number;
  dailyBreakdown: DailySalesData[];
  comparisonToPreviousWeek: ComparisonData;
}

interface MonthlySalesData extends DailySalesData {
  monthStart: string;
  monthEnd: string;
  weeklyBreakdown: WeeklySalesData[];
  comparisonToPreviousMonth: ComparisonData;
}

interface CustomerPayment {
  name: string;
  email: string;
  status: 'succeeded' | 'failed' | 'pending';
  description: string;
  timestamp: string;
}

interface ComparisonData {
  paymentCountChange: number;
  paymentCountPercentChange: number;
  successRateChange: number;
}

class SalesReportingService {
  private slack: WebClient;
  private stripe: Stripe;

  // Core reporting methods
  async sendDailySalesReport(): Promise<void>;
  async sendWeeklySalesReport(): Promise<void>;
  async sendMonthlySalesReport(): Promise<void>;
  
  // Data generation methods
  private async generateDailySalesData(date: string): Promise<DailySalesData>;
  private async generateWeeklySalesData(weekStart: DateTime): Promise<WeeklySalesData>;
  private async generateMonthlySalesData(monthStart: DateTime): Promise<MonthlySalesData>;
  
  // Slack formatting methods
  private formatDailySlackBlocks(data: DailySalesData): (Block | KnownBlock)[];
  private formatWeeklySlackBlocks(data: WeeklySalesData): (Block | KnownBlock)[];
  private formatMonthlySlackBlocks(data: MonthlySalesData): (Block | KnownBlock)[];
  
  // Utility methods
  private async sendToSlack(blocks: (Block | KnownBlock)[], reportType: string): Promise<void>;
  private calculateComparison(current: DailySalesData, previous: DailySalesData): ComparisonData;
}
```

### 2. Database Layer

**File**: `migrations/create_stripe_payments_table.sql`

```sql
CREATE TABLE IF NOT EXISTS stripe_payments (
  id SERIAL PRIMARY KEY,
  stripe_payment_intent_id VARCHAR(255) UNIQUE NOT NULL,
  customer_name VARCHAR(255),
  customer_email VARCHAR(255) NOT NULL,
  amount_cents INTEGER NOT NULL,
  currency VARCHAR(10) DEFAULT 'AUD',
  status VARCHAR(50) NOT NULL,
  description TEXT,
  metadata JSONB,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  -- Indexes for efficient querying
  INDEX idx_stripe_payments_created_at (created_at),
  INDEX idx_stripe_payments_status (status),
  INDEX idx_stripe_payments_email (customer_email)
);
```

**Database Service Methods**:
```typescript
// In salesReporting.service.ts
private async storePaymentData(paymentData: StripePaymentData): Promise<void>;
private async getPaymentsForDateRange(startDate: string, endDate: string): Promise<CustomerPayment[]>;
private async getPaymentStats(startDate: string, endDate: string): Promise<PaymentStats>;
```

### 3. API Controllers

**File**: `apps/api/src/controllers/sales/index.ts`

```typescript
export const triggerDailySalesReport: RequestHandler = catchAll(async (_req, res) => {
  const salesService = new SalesReportingService();
  await salesService.sendDailySalesReport();
  res.json({ 
    success: true, 
    message: 'Daily sales report sent',
    timestamp: new Date().toISOString()
  });
});

export const triggerWeeklySalesReport: RequestHandler = catchAll(async (_req, res) => {
  const salesService = new SalesReportingService();
  await salesService.sendWeeklySalesReport();
  res.json({ 
    success: true, 
    message: 'Weekly sales report sent',
    timestamp: new Date().toISOString()
  });
});

export const triggerMonthlySalesReport: RequestHandler = catchAll(async (_req, res) => {
  const salesService = new SalesReportingService();
  await salesService.sendMonthlySalesReport();
  res.json({ 
    success: true, 
    message: 'Monthly sales report sent',
    timestamp: new Date().toISOString()
  });
});

export const getSalesReportStatus: RequestHandler = catchAll(async (_req, res) => {
  // Return system status and configuration
});

export const testSalesReport: RequestHandler = catchAll(async (req, res) => {
  // Test report with specific date range
});
```

### 4. Enhanced Webhook Handler

**Enhancement to**: `apps/api/src/controllers/funnel/index.ts`

```typescript
// Enhanced setZohoInvoicePaymentStatus function
export const setZohoInvoicePaymentStatus: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  
  try {
    await client.query('BEGIN');
    
    // Existing Zoho processing logic...
    const body = req.body;
    const payment_status = body.type;
    const payment_description = body.data.object.description;
    const name = body.data.object.metadata.Name;
    const email = body.data.object.metadata.email;
    const amount = body.data.object.amount_received;
    
    // NEW: Store payment data for reporting
    await storeStripePaymentData(client, {
      stripePaymentIntentId: body.data.object.id,
      customerName: name,
      customerEmail: email,
      amountCents: amount,
      currency: body.data.object.currency || 'AUD',
      status: payment_status,
      description: payment_description,
      metadata: body.data.object.metadata
    });
    
    // Existing Zoho CRM update logic...
    const result = await ZohoAuth.setZohoInvoicePaymentStatusFromStripe(
      payment_status,
      payment_description,
      name,
      email,
      amount / 100,
      message
    );
    
    await client.query('COMMIT');
    
    if (result != null) {
      res.status(200).send(result);
    } else {
      res.status(400).send('No payment found');
    }
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error('Error processing Stripe webhook:', error);
    throw new ApiError(httpStatus.BAD_REQUEST, 'Webhook processing failed');
  } finally {
    client.release();
  }
});

// NEW: Helper function for storing payment data
async function storeStripePaymentData(client: PoolClient, paymentData: StripePaymentData): Promise<void> {
  const query = `
    INSERT INTO stripe_payments 
    (stripe_payment_intent_id, customer_name, customer_email, amount_cents, currency, status, description, metadata)
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    ON CONFLICT (stripe_payment_intent_id) 
    DO UPDATE SET 
      status = EXCLUDED.status,
      processed_at = CURRENT_TIMESTAMP
  `;
  
  await client.query(query, [
    paymentData.stripePaymentIntentId,
    paymentData.customerName,
    paymentData.customerEmail,
    paymentData.amountCents,
    paymentData.currency,
    paymentData.status,
    paymentData.description,
    JSON.stringify(paymentData.metadata)
  ]);
}
```

### 5. Route Configuration

**File**: `apps/api/src/routes/sales.route.ts`

```typescript
import express from 'express';
import {
  triggerDailySalesReport,
  triggerWeeklySalesReport,
  triggerMonthlySalesReport,
  getSalesReportStatus,
  testSalesReport
} from '../controllers/sales';

const router = express.Router();
const currentVersion = 'v1';

// Cron job trigger endpoints (no auth required for server-level cron)
router.post(`/${currentVersion}/trigger-daily-report`, triggerDailySalesReport);
router.post(`/${currentVersion}/trigger-weekly-report`, triggerWeeklySalesReport);
router.post(`/${currentVersion}/trigger-monthly-report`, triggerMonthlySalesReport);

// Status and testing endpoints
router.get(`/${currentVersion}/status`, getSalesReportStatus);
router.post(`/${currentVersion}/test-report`, testSalesReport);

export default router;
```

## Data Models

### Stripe Payment Data Model

```typescript
interface StripePaymentData {
  stripePaymentIntentId: string;
  customerName: string;
  customerEmail: string;
  amountCents: number;
  currency: string;
  status: string;
  description: string;
  metadata: Record<string, any>;
}

interface PaymentStats {
  totalPayments: number;
  successfulPayments: number;
  failedPayments: number;
  totalAmountCents: number;
  averageAmountCents: number;
  successRate: number;
}
```

### Report Data Models

```typescript
interface ReportPeriod {
  startDate: string;
  endDate: string;
  timezone: string;
}

interface SalesMetrics {
  payments: PaymentStats;
  customers: CustomerPayment[];
  trends: TrendData;
  comparisons: ComparisonData;
}
```

## Error Handling

### Transaction Management
- All database operations wrapped in transactions
- Rollback on any failure to maintain data integrity
- Separate transactions for webhook processing vs report generation

### Retry Logic
- Exponential backoff for Slack API calls
- Retry failed database connections
- Circuit breaker pattern for external API calls

### Error Logging
```typescript
// Structured error logging
logger.error('Sales report generation failed', {
  reportType: 'daily',
  date: reportDate,
  error: error.message,
  stack: error.stack,
  context: { userId, requestId }
});
```

### Graceful Degradation
- Continue webhook processing even if reporting storage fails
- Send partial reports if some data is unavailable
- Fallback to basic text format if Slack block formatting fails

## Testing Strategy

### Unit Tests
- Test report data generation with mock data
- Test Slack block formatting
- Test database operations with test database
- Test error handling scenarios

### Integration Tests
- Test complete webhook-to-report flow
- Test cron job endpoint calls
- Test Slack API integration
- Test database transaction handling

### Performance Tests
- Test report generation with large datasets
- Test concurrent webhook processing
- Test database query performance with indexes

## Security Considerations

### Data Privacy
- Store customer names but not payment amounts in logs
- Use environment variables for all API keys
- Implement data retention policies for old payment records

### Access Control
- Cron job endpoints accessible only from server
- Test endpoints require authentication in production
- Slack channels configured with appropriate permissions

### Input Validation
- Validate all webhook payloads
- Sanitize customer data before storage
- Validate date ranges for report generation

## Configuration Management

### Environment Variables
```typescript
// Additional config in apps/api/src/config/index.ts
export default {
  // ... existing config ...
  
  // Sales reporting configuration
  slackSalesReportChannel: process.env.SLACK_SALES_REPORT_CHANNEL ?? '',
  salesReportingEnabled: process.env.SALES_REPORTING_ENABLED === 'true',
  salesDataRetentionDays: parseInt(process.env.SALES_DATA_RETENTION_DAYS ?? '365', 10),
  
  // Cron job configuration
  dailyReportCron: process.env.DAILY_REPORT_CRON ?? '0 9 * * *',
  weeklyReportCron: process.env.WEEKLY_REPORT_CRON ?? '0 9 * * 1',
  monthlyReportCron: process.env.MONTHLY_REPORT_CRON ?? '0 9 1 * *',
};
```

### Feature Flags
- Enable/disable reporting per environment
- Toggle between test and production Stripe keys
- Control report frequency and content