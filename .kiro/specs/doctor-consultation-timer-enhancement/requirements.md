# Requirements Document

## Introduction

This feature enhances the existing doctor consultation timer functionality by expanding the pause trigger conditions to include when the doctor focuses on the notes input field, in addition to when typing. Currently, the system only pauses timers when the doctor is actively typing, but doctors often need to focus on the notes field for activities like reading, reviewing, or preparing to type without actually typing yet. The system should maintain a single unified pause state that can be triggered by either typing OR focusing on the notes field.

## Requirements

### Requirement 1

**User Story:** As a doctor, I want the consultation timers to pause when I focus on the notes input field OR when I'm typing, so that I have adequate time to read, review, and prepare my notes without being rushed by countdown timers.

#### Acceptance Criteria

1. WHEN the doctor clicks on or focuses the notes input field OR starts typing THEN the system SHALL activate a single unified pause state for all active countdown timers
2. WHEN the doctor focuses on the notes input field OR starts typing THEN the system SHALL log this action for audit purposes
3. WHEN the doctor is focused on the notes input field OR typing AND is alone on the call THEN the system SHALL maintain the unified paused state of all timers
4. WHEN the unified pause state is activated THEN the system SHALL apply the same 5-second buffer rule that exists for timing detection
5. WHEN the doctor unfocuses from the notes input field AND stops typing THEN the system SHALL resume countdown timers after a 15-second delay

### Requirement 2

**User Story:** As a doctor, I want a single, seamless timer pause experience that works whether I'm typing or just focused on the notes field, so that I don't experience timer conflicts or unexpected behavior.

#### Acceptance Criteria

1. WHEN the doctor is typing in the notes field THEN the system SHALL maintain the unified pause state
2. WHEN the doctor stops typing but remains focused on the notes field THEN the system SHALL continue the unified pause state
3. WHEN the doctor is focused on the notes field AND starts typing THEN the system SHALL maintain the same unified pause state without creating duplicate states
4. WHEN the doctor unfocuses from the notes field while still typing THEN the system SHALL continue the unified pause state
5. WHEN both focus and typing end THEN the system SHALL use a single 15-second delay before resuming timers from the unified pause state

### Requirement 3

**User Story:** As a system administrator, I want the unified timer pause to follow consistent rules regardless of whether it's triggered by typing or focus, so that the system behavior remains predictable.

#### Acceptance Criteria

1. WHEN the doctor focuses on notes OR types while other participants are present THEN the system SHALL NOT activate the unified pause state
2. WHEN the doctor focuses on notes OR types while alone on the call THEN the system SHALL activate the unified pause state for all active countdown timers
3. WHEN timers are in unified pause state and were ≤ 1 second THEN the system SHALL apply a 5-second buffer when resuming
4. WHEN the unified pause state is active THEN the system SHALL pause the same timer types (patient dropout, patient not joined, final countdown, buffer countdown)
5. WHEN the unified pause state ends THEN the system SHALL resume timers with the same 15-second delay logic

### Requirement 4

**User Story:** As a doctor, I want clear visual feedback about the unified timer pause state, so that I understand when timers are paused regardless of whether I'm typing or focused on the notes field.

#### Acceptance Criteria

1. WHEN the unified pause state is active THEN the system SHALL provide consistent visual indicators regardless of trigger (typing or focus)
2. WHEN timers are in unified pause state THEN the system SHALL maintain existing countdown displays but pause their progression
3. WHEN the unified pause state is active THEN the system SHALL show consistent pause indicators across all affected timers
4. WHEN the doctor transitions between typing and focus THEN the system SHALL maintain the same visual pause indicators
5. WHEN the 15-second resume delay starts THEN the system SHALL provide appropriate visual feedback

### Requirement 5

**User Story:** As a system administrator, I want comprehensive logging of unified timer pause interactions, so that I can monitor and troubleshoot the enhanced timer functionality.

#### Acceptance Criteria

1. WHEN the unified pause state activates due to focus THEN the system SHALL log "Doctor focused on notes - activating unified pause"
2. WHEN the unified pause state activates due to typing THEN the system SHALL log "Doctor started typing - activating unified pause"
3. WHEN the unified pause state continues due to multiple triggers THEN the system SHALL log the transition clearly
4. WHEN the unified pause state ends THEN the system SHALL log "Doctor activity ended - starting 15s resume delay"
5. WHEN 5-second buffer is applied THEN the system SHALL log the buffer application with before/after values

### Requirement 6

**User Story:** As a doctor, I want the unified timer pause to work reliably across different browsers and devices, so that I can depend on this functionality regardless of my technical setup.

#### Acceptance Criteria

1. WHEN using Chrome, Firefox, Safari, or Edge THEN the focus detection SHALL work consistently as part of the unified pause system
2. WHEN using touch devices THEN the focus detection SHALL work with touch-based interactions
3. WHEN using keyboard navigation THEN the focus detection SHALL work with tab-based focus
4. WHEN the browser window loses focus THEN the notes field focus state SHALL be handled appropriately within the unified pause system
5. WHEN returning to the browser window THEN the unified pause state SHALL be restored correctly