# Design Document

## Overview

This design extends the existing doctor consultation timer system to implement a unified pause mechanism that can be triggered by either typing activity OR focusing on the notes input field. The enhancement transforms the current typing-only detection into a comprehensive pause system that responds to multiple doctor activities while maintaining a single, consistent pause state. The design maintains backward compatibility while providing more comprehensive timer pause coverage for doctor note-taking activities.

## Architecture

### Current System Analysis

The existing system implements timer pause functionality through:
- Typing detection with 15-second delay mechanism
- Multiple countdown timers (patient dropout, patient not joined, final countdown, buffer countdown)
- Pause/resume logic with 5-second buffer rule for timers ≤ 1 second
- Participant count checking to ensure pause only when doctor is alone

### Enhanced Architecture

The enhanced system transforms the current architecture by:
- Adding focus detection as an additional trigger for the existing pause mechanism
- Creating a unified pause state that can be activated by either typing OR focus
- Implementing trigger tracking to manage multiple activation conditions
- Maintaining a single pause/resume cycle regardless of trigger combination

```mermaid
graph TD
    A[Notes Input Field] --> B[Focus Detection]
    A --> C[Typing Detection]
    B --> D[Unified Pause Controller]
    C --> D
    D --> E{Doctor Alone?}
    E -->|Yes| F[Activate Unified Pause State]
    E -->|No| G[No Action]
    F --> H[Pause All Timers]
    H --> I[Patient Dropout Timer]
    H --> J[Patient Not Joined Timer]
    H --> K[Final Countdown Timer]
    H --> L[Buffer Countdown Timer]
    D --> M[Activity End Detection]
    M --> N[15s Resume Delay]
    N --> O[Resume All Timers]
    O --> P[Apply 5-Second Buffer Rule]
```

## Components and Interfaces

### 1. Unified Pause Controller

**Purpose:** Manage a single pause state that can be triggered by multiple activities (typing or focus).

**Interface:**
```typescript
interface UnifiedPauseController {
  isPaused: boolean;
  activeTriggers: Set<'typing' | 'focus'>;
  addTrigger: (trigger: 'typing' | 'focus') => void;
  removeTrigger: (trigger: 'typing' | 'focus') => void;
  shouldPause: () => boolean;
  shouldResume: () => boolean;
}
```

**State Logic:**
- `isPaused = activeTriggers.size > 0 && isDoctorAloneOnCall()`
- Tracks which activities are currently active
- Manages single pause state regardless of trigger count

### 2. Activity Detection Component

**Purpose:** Detect both typing and focus activities and report to the unified controller.

**Interface:**
```typescript
interface ActivityDetection {
  onTypingStart: () => void;
  onTypingEnd: () => void;
  onFocusStart: () => void;
  onFocusEnd: () => void;
  isTyping: boolean;
  isFocused: boolean;
}
```

**Implementation Details:**
- Uses existing typing detection logic
- Adds standard DOM focus/blur event listeners
- Reports activity changes to unified controller
- Handles edge cases like window focus loss

### 3. Enhanced Timer Controller

**Purpose:** Control timer pause/resume based on unified pause state.

**Interface:**
```typescript
interface EnhancedTimerController {
  pauseAllCountdowns: () => boolean;
  resumeAllCountdowns: () => void;
  handleUnifiedStateChange: (isPaused: boolean, triggers: Set<string>) => void;
}
```

**Enhanced Logic:**
- Uses existing pause/resume functions
- Responds to unified pause state changes
- Maintains existing 5-second buffer rule
- Single resume timer management

### 4. Activity Event Handlers

**Purpose:** Handle typing and focus events and coordinate with unified controller.

**Implementation:**
```typescript
const handleActivityStart = (activityType: 'typing' | 'focus') => {
  unifiedController.addTrigger(activityType);
  
  if (unifiedController.shouldPause() && !unifiedController.isPaused) {
    pauseAllCountdowns();
    ApiClient.userActions('Doctor', token || '', UserActions.UNIFIED_PAUSE_START, 
      `Doctor ${activityType} activity - activating unified pause`);
  }
  
  // Clear any existing resume timer since activity is active
  if (resumeTimeoutRef.current) {
    clearTimeout(resumeTimeoutRef.current);
    resumeTimeoutRef.current = undefined;
  }
};

const handleActivityEnd = (activityType: 'typing' | 'focus') => {
  unifiedController.removeTrigger(activityType);
  
  // Only start resume timer if no other activities are active
  if (unifiedController.shouldResume()) {
    startResumeTimer();
    ApiClient.userActions('Doctor', token || '', UserActions.UNIFIED_PAUSE_END, 
      'Doctor activity ended - starting 15s resume delay');
  }
};

const startResumeTimer = () => {
  resumeTimeoutRef.current = setTimeout(() => {
    if (unifiedController.shouldResume()) {
      resumeAllCountdowns();
      ApiClient.userActions('Doctor', token || '', UserActions.UNIFIED_RESUME, 
        'Unified pause ended - resuming countdowns after 15s');
    }
  }, 15000);
};
```

## Data Models

### Unified Pause State Model

```typescript
interface UnifiedPauseState {
  // Unified pause state
  isPaused: boolean;
  activeTriggers: Set<'typing' | 'focus'>;
  resumeTimeoutRef: React.MutableRefObject<NodeJS.Timeout | undefined>;
  
  // Activity tracking
  isTyping: boolean;
  isFocused: boolean;
  
  // Existing pause tracking (unchanged)
  pausedCountdowns: {
    patientDropout: boolean;
    patientNotJoined: boolean;
    finalCountdown: boolean;
    bufferCountdown: boolean;
  };
  
  // Existing pre-pause values for buffer rule (unchanged)
  prePauseValues: {
    patientDropout: number;
    patientNotJoined: number;
    finalCountdown: number;
    bufferCountdown: number;
  };
}
```

### User Action Types Extension

```typescript
enum UserActions {
  // Existing actions (keep for backward compatibility)
  TYPING_START = 'typing_start',
  TYPING_END = 'typing_end',
  
  // New unified actions
  UNIFIED_PAUSE_START = 'unified_pause_start',
  UNIFIED_PAUSE_END = 'unified_pause_end',
  UNIFIED_RESUME = 'unified_resume',
  
  // Activity-specific actions for detailed logging
  FOCUS_ACTIVITY_START = 'focus_activity_start',
  FOCUS_ACTIVITY_END = 'focus_activity_end',
  TYPING_ACTIVITY_START = 'typing_activity_start',
  TYPING_ACTIVITY_END = 'typing_activity_end'
}
```

## Error Handling

### Activity Detection Errors

1. **Event Listener Failures:**
   - Fallback to typing-only detection
   - Log error but continue operation
   - Graceful degradation to existing functionality

2. **State Synchronization Issues:**
   - Periodic state validation
   - Reset mechanism for corrupted unified state
   - Logging for debugging

3. **Browser Compatibility Issues:**
   - Feature detection for focus events
   - Polyfills for older browsers
   - Alternative detection methods

### Unified State Conflicts

1. **Trigger Management Issues:**
   - Automatic cleanup of stale triggers
   - State reconciliation logic
   - Conflict resolution logging

2. **Resume Timer Conflicts:**
   - Single resume timer management (existing logic)
   - Clear existing timers before setting new ones
   - State cleanup on component unmount

## Testing Strategy

### Unit Tests

1. **Unified Pause Controller Tests:**
   ```typescript
   describe('Unified Pause Controller', () => {
     test('should activate pause when first trigger added');
     test('should maintain pause when multiple triggers active');
     test('should only resume when all triggers removed');
     test('should handle rapid trigger add/remove');
   });
   ```

2. **Activity Detection Tests:**
   ```typescript
   describe('Activity Detection', () => {
     test('should detect typing and focus independently');
     test('should report activity changes to unified controller');
     test('should handle overlapping activities correctly');
     test('should work with keyboard navigation');
   });
   ```

3. **Enhanced Pause Logic Tests:**
   ```typescript
   describe('Enhanced Pause Logic', () => {
     test('should pause on any activity when doctor alone');
     test('should not pause when others present');
     test('should apply 5-second buffer correctly');
     test('should use single resume timer');
   });
   ```

### Integration Tests

1. **Timer Integration:**
   - Test all timer types pause/resume with unified state
   - Verify buffer rule application
   - Test activity transitions

2. **User Action Logging:**
   - Verify unified pause events are logged
   - Test activity-specific logging
   - Validate audit trail completeness

3. **Browser Compatibility:**
   - Test across Chrome, Firefox, Safari, Edge
   - Verify touch device compatibility
   - Test keyboard navigation scenarios

### End-to-End Tests

1. **Doctor Workflow Tests:**
   - Complete consultation with unified pause
   - Mixed typing and focus activities
   - Timer behavior during note-taking sessions

2. **Edge Case Tests:**
   - Rapid activity changes
   - Browser window focus loss/gain
   - Component unmount during active pause

## Implementation Considerations

### Performance

- Minimal overhead from unified state management
- Efficient trigger tracking using Set data structure
- Single resume timer reduces resource usage

### Accessibility

- Maintain keyboard navigation compatibility
- Ensure screen reader compatibility
- Support for assistive technologies

### Browser Compatibility

- Standard focus/blur events (widely supported)
- Graceful degradation for older browsers
- Feature detection and polyfills where needed

### Maintenance

- Simplified state management with unified approach
- Clear separation of activity detection and pause logic
- Backward compatibility with existing timer system

## Migration Strategy

### Phase 1: Core Implementation
- Implement unified pause controller
- Add focus detection to notes input field
- Integrate with existing typing detection

### Phase 2: State Management
- Replace separate pause states with unified state
- Implement trigger tracking system
- Update logging to use unified actions

### Phase 3: Testing & Refinement
- Comprehensive testing across browsers
- Performance optimization
- Bug fixes and edge case handling

### Phase 4: Deployment
- Feature flag for gradual rollout
- Monitoring and analytics
- Documentation updates