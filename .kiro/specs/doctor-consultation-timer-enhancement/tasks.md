# Implementation Plan

- [x] 1. Implement unified pause state management
  - Add unified pause state variables to replace separate typing/focus pause states
  - Create activeTriggers Set to track which activities (typing/focus) are currently active
  - Add single resumeTimeoutRef to manage the 15-second resume delay
  - Initialize unified pause controller with proper TypeScript interfaces
  - _Requirements: 1.1, 2.1, 3.4_

- [x] 2. Create unified pause controller logic
  - Implement addTrigger function to add typing or focus activities to active set
  - Implement removeTrigger function to remove activities from active set
  - Create shouldPause logic: activeTriggers.size > 0 && isDoctorAloneOnCall()
  - Create shouldResume logic: activeTriggers.size === 0
  - _Requirements: 1.3, 2.3, 3.1, 3.2_

- [x] 3. Implement focus detection for notes input field
  - Add focus and blur event listeners to notes input field in PatientForm component
  - Create handleNotesFocus function to call unified controller addTrigger('focus')
  - Create handleNotesBlur function to call unified controller removeTrigger('focus')
  - Ensure proper event listener cleanup on component unmount
  - _Requirements: 1.1, 1.2, 6.3_

- [x] 4. Integrate focus detection with existing typing detection
  - Modify existing handleTypingStart to call unified controller addTrigger('typing')
  - Modify existing handleTypingStop to call unified controller removeTrigger('typing')
  - Remove separate typing timeout logic in favor of unified approach
  - Ensure typing detection continues to work exactly as before
  - _Requirements: 2.1, 2.4, 3.5_

- [x] 5. Update pause/resume logic to use unified state
  - Modify pauseAllCountdowns to be triggered by unified pause state activation
  - Update resumeAllCountdowns to be triggered by unified pause state deactivation
  - Ensure single resume timer is used regardless of which activity ends last
  - Maintain existing 5-second buffer rule for timers ≤ 1 second
  - _Requirements: 1.4, 1.5, 2.5, 3.3_

- [x] 6. Implement unified activity event handlers
  - Create handleActivityStart function that adds trigger and activates pause if needed
  - Create handleActivityEnd function that removes trigger and starts resume if needed
  - Implement single startResumeTimer function with 15-second delay
  - Clear existing resume timer when any new activity starts
  - _Requirements: 1.5, 2.2, 2.5, 3.5_

- [x] 7. Add comprehensive logging for unified pause system
  - Log "Doctor [activity] activity - activating unified pause" when pause starts
  - Log "Doctor activity ended - starting 15s resume delay" when last activity ends
  - Log "Unified pause ended - resuming countdowns after 15s" when timers resume
  - Log 5-second buffer applications with before/after values
  - Maintain backward compatibility with existing typing logs
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

- [x] 8. Update PatientForm component for focus event integration
  - Identify the notes textarea/input field in PatientForm component
  - Add onFocus and onBlur props to PatientForm component interface
  - Pass unified controller event handlers from DoctorConsultation to PatientForm
  - Attach focus/blur event listeners to the notes input field
  - _Requirements: 1.1, 6.1, 6.2_
- [x] 9. Implement notes detection for timer extension
  - Create helper function to check if doctor has entered notes in the treatment plan
  - Access patientTreatmentPlan.drNotes to determine if notes are present
  - Handle edge cases like empty strings, whitespace-only notes, and undefined values
  - Add logging for notes detection status for debugging purposes
  - _Requirements: New requirement - notes-based timer extension_

- [x] 10. Create dynamic timer duration logic
  - Implement getRedirectTimerDuration function that returns 120 seconds if notes present, 30 seconds if not
  - Update timer creation functions to use dynamic duration instead of hardcoded 30 seconds
  - Ensure timer duration is determined at the moment the timer starts, not when component loads
  - Add comprehensive logging for timer duration decisions
  - _Requirements: New requirement - 2 minutes with notes, 30 seconds without_

- [x] 11. Implement UI freeze mechanism for extended timers
  - Create state variable to track when UI should show frozen timer display
  - Implement logic to freeze countdown display at 30 seconds when extended timer is running
  - Ensure visual countdown stops updating while background timer continues for full duration
  - Add mechanism to resume visual countdown if timer gets paused/resumed by unified pause system
  - _Requirements: New requirement - hide timer extension from doctor_

- [x] 12. Update patient dropout countdown with notes-based extension
  - Modify startPatientDropoutCountdown to use dynamic timer duration based on notes presence
  - Update UI to show frozen countdown at 30 seconds when running extended 2-minute timer
  - Ensure background timer runs for full 2 minutes while UI shows frozen state
  - Test that patient dropout logic works correctly with both 30-second and 2-minute durations
  - _Requirements: New requirement - patient dropout timer extension_

- [x] 13. Update patient not joined countdown with notes-based extension
  - Modify startPatientNotJoinedCountdown to use dynamic timer duration based on notes presence
  - Implement UI freeze mechanism for patient not joined countdown display
  - Ensure nextPatientTimer uses correct duration (30s or 120s) based on notes presence
  - Add logging to track when extended timers are used vs standard timers
  - _Requirements: New requirement - patient not joined timer extension_

- [x] 14. Enhance unified pause system for extended timers
  - Update pauseAllCountdowns to handle both visual and background timer states
  - Ensure resumeAllCountdowns works correctly with frozen UI and extended background timers
  - Apply 5-second buffer rule to actual timer duration, not just visual display
  - Maintain unified pause functionality regardless of timer extension state
  - _Requirements: Integration with existing unified pause system_

- [x] 15. Add comprehensive logging for notes-based timer behavior
  - Log when extended 2-minute timers are activated due to notes presence
  - Log when standard 30-second timers are used due to no notes
  - Log UI freeze activation and deactivation for extended timers
  - Add debugging information for timer duration decisions and background vs visual timer states
  - _Requirements: Audit trail for notes-based timer extension feature_

- [x] 16. Implement edge case handling for notes-based timers
  - Handle scenario where notes are added/removed while timer is running
  - Ensure timer extension only applies to redirect timers, not consultation timers
  - Add error handling for cases where notes detection fails
  - _Requirements: Robust handling of edge cases_

  - [ ] 17. Check for linting errors and fix any found
